#Cache name and expiry in seconds
twitterHomeTweets=120
facebookPlaces=3600
instagramLocationDetails=3600
fb.pages=86400
facebookPageDetailsByPageIdCache=86400
businessCache=3600
linksPreviewCache=86400
# Google access Token cache.
accessTokenCache=900
# Website domain information cache for business.
domainCache=1800
# PlaceId against LocationId cache for get review by Id.
placeIdCache=604800
# Google Messages credential cache
googleMessagesCreds=3300
# Google Messages Agent info
googleMessagesAgent=3600
# Google Messages status info
gMsgStatus=3600
# Google All Attributes List Cache
googleAllAttributesList=86400
# Google Access token cache
getGoogleAccessTokenCache=3500
# Link in bio creation Cache
createLinkInBio=3600
business.core=86400
businessLiteInfo=86400
permissionMappingByChannel=10800
user.info.cache=3600
user.full.name=3600
ytRefreshTokenCache=3000
btpDayWiseCache=7200
btpCountryChannelCategoryCache=7200
# Optimized cache for BTP day-wise data with longer TTL for better performance
btpDayWiseOptimizedCache=14400
tiktokAccessToken=3300

# ---------- Asset Library Cache ------------
# Asset Library Asset to parent folder cache
assetLibAssetToParentFolder=2629743
# Asset Library assets by filter cache
assetLibAssetsByFilter=3600
# Asset Library asset filter hashes by account
assetLibFilterHashListByAccount=7200
# Asset Library All tags by account
assetLibAllBizTagCache=604800
# Asset Library Asset Names By Type in Folder
assetLibAllAssetNamesInFolderByTypeCache=600
# ----------------------------------------------

# ----------- Social Tag(s) Cache --------------
# ALL Tag(s) by account
accountAllSocialTagCache=604800
# ----------------------------------------------
