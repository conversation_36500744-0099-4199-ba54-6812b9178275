package com.birdeye.social.external.btp;

import com.birdeye.social.dao.btp.BTPDayWiseDataRepo;
import com.birdeye.social.entities.btp.BTPDayWiseData;
import com.birdeye.social.entities.btp.BTPPriorityLookup;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class BTPDayWiseServiceImpl implements BTPDayWiseService{

    @Autowired
    private BTPDayWiseDataRepo btpDayWiseDataRepo;

    private static final Logger logger = LoggerFactory.getLogger(BTPDayWiseServiceImpl.class);


    @Override
    public void save(BTPDayWiseData btpDayWiseData) {
        btpDayWiseDataRepo.save(btpDayWiseData);
    }

    @Override
    public void saveList(List<BTPDayWiseData> btpDayWiseDataList) {
        if(CollectionUtils.isEmpty(btpDayWiseDataList)){
            return;
        }
        btpDayWiseDataList.forEach(data -> btpDayWiseDataRepo.save(data));
    }

    @Override
    @Cacheable(value = "btpCountryChannelCategoryCache",
            key = "#lookup.country + ':' + #lookup.channel + ':' + #lookup.category + ':' + #reportType + ':' + #fromTime + ':' + #toTime + ':' + #limit",
            unless = "#result == null || #result.isEmpty()"
    )
    public List<BTPDayWiseData> findByCountryAndChannelAndCategoryAndGreaterThanFromTimeAndReportType(
            BTPPriorityLookup lookup, Date fromTime,Date toTime, String reportType,Integer limit) {
        logger.info("--------[remove log] request for data: {}, {}, {}, {}, {}, {}, {}", lookup.getCountry(),lookup.getCategory(),lookup.getChannel(),reportType,
                fromTime,toTime,limit);
        return btpDayWiseDataRepo.findByCountryAndChannelAndCategoryAndReportTypeAndDayGreaterThan(lookup.getCountry(),
                lookup.getChannel(),lookup.getCategory(),reportType,fromTime,toTime,limit);
    }

    @Override
    public List<BTPDayWiseData> findByCountryAndChannelAndCategoryAndGreaterThanFromTimeAndReportTypeDB(
            BTPPriorityLookup lookup, Date fromTime,Date toTime, String reportType,Integer limit) {
        return btpDayWiseDataRepo.findByCountryAndChannelAndCategoryAndReportTypeAndDayGreaterThan(lookup.getCountry(),
                lookup.getChannel(),lookup.getCategory(),reportType,fromTime,toTime,limit);
    }

    @Override
    @Cacheable(value = "btpDayWiseCache",
            key = "#lookup.country + ':' + #lookup.channel + ':' + #lookup.category + ':' + #reportType + ':' + #currentTime + ':' + #toTime",
            unless = "#result == null || #result.isEmpty()"
    )
    public List<BTPDayWiseData> findByTop3TimesPerDay(BTPPriorityLookup lookup,String reportType, Date currentTime,Date toTime) {
        logger.info("-------[remove log] request for 3 times data: {}, {}, {}, {}, {}, {}", lookup.getCountry(),lookup.getCategory(),lookup.getChannel(),reportType,
                currentTime,toTime);
        return btpDayWiseDataRepo.findTop3TimesPerDay(lookup.getCountry(),
                lookup.getChannel(),lookup.getCategory(),reportType,currentTime,toTime);
    }

    @Override
    public List<BTPDayWiseData> findByTop3TimesPerDayDB(BTPPriorityLookup lookup,String reportType, Date currentTime,Date toTime) {
        return btpDayWiseDataRepo.findTop3TimesPerDay(lookup.getCountry(),
                lookup.getChannel(),lookup.getCategory(),reportType,currentTime,toTime);
    }



    @Override
    public List<BTPDayWiseData> findByTop3TimesPerWeek(BTPPriorityLookup lookup, String reportType,
                                                       Date currentTime,Date weekEndTime) {
        return btpDayWiseDataRepo.findTop3TimesPerWeek(lookup.getCountry(),
                lookup.getChannel(),lookup.getCategory(),reportType,currentTime,weekEndTime);
    }
}
