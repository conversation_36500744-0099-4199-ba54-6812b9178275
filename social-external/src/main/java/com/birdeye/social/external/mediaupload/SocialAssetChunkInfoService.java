package com.birdeye.social.external.mediaupload;

import com.birdeye.social.entities.mediaupload.SocialAssetChunkInfo;

import java.util.List;

public interface SocialAssetChunkInfoService {
    boolean existsByAssetId(Integer assetId);

    void save(SocialAssetChunkInfo socialAssetChunkInfo);

    Integer getCountOfAssetChunks(Integer assetId, Integer sourceId);

    SocialAssetChunkInfo findByAssetIdAndSequenceIdAndSourceId(Integer assetId, Integer sequenceId, Integer sourceId);

    List<SocialAssetChunkInfo> findByAssetId(Integer assetId);
    List<SocialAssetChunkInfo> findByAssetIdAndSourceId(Integer assetId, Integer sourceId);
    List<Integer> findSequenceIdsByAssetIdAndSourceId(Integer assetId, Integer sourceId);
}
