package com.birdeye.social.service.SocialReportService;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dao.reports.BusinessPostsRepository;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.report.BusinessPostMetadata;
import com.birdeye.social.dto.report.PagePostData;
import com.birdeye.social.dto.report.PostInsightsEventRequest;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.factory.ChannelPostServiceFactory;
import com.birdeye.social.factory.arbor.ArborServiceFactory;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.*;
import com.birdeye.social.insights.ES.Request.ESPageRequest;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.insights.constants.InsightsConstants;
import com.birdeye.social.model.*;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.service.*;
import com.birdeye.social.service.SocialReportService.Dedupe.SocialPostDedupeService;
import com.birdeye.social.service.SocialReportService.ES.ReportsEsService;
import com.birdeye.social.service.SocialReportService.ExternalService.FacebookExternalService;
import com.birdeye.social.service.SocialReportService.Facebook.FacebookInsights;
import com.birdeye.social.service.SocialReportService.GMB.GmbReportService;
import com.birdeye.social.service.SocialReportService.Instagram.InstagramReportService;
import com.birdeye.social.service.SocialReportService.Tiktok.TiktokInsights;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.birdeye.social.sro.SocialTagEntityMappingActionEvent;
import com.birdeye.social.utils.ConversionUtils;
import com.birdeye.social.utils.DateTimeUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.BadRequestException;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.birdeye.social.cache.SystemPropertiesCache.SIZE_LIMIT_DAILY_SYNC;
import static com.birdeye.social.constant.Constants.FACEBOOK;
import static com.birdeye.social.constant.Constants.TIKTOK;
import static com.birdeye.social.insights.ES.SearchTemplate.*;

@Service
public class SocialReportServiceImpl implements SocialReportService{

    @Autowired
    private InsightsFactory socialInsights;

    @Autowired
    private EsService esService;

    @Autowired
    private  TiktokInsights tiktokInsights;

    @Autowired
    FacebookInsights facebookInsights;

    @Autowired
    SocialInsightsAuditRepository insightsAuditRepository;

    @Autowired
    private SocialPostDedupeService dedupeService;

    @Autowired
    private BusinessPostsRepository businessPostsRepository;

    @Autowired
    private KafkaProducerService kafkaProducer;

    @Autowired
    private ReportsEsService reportsEsService;

    @Autowired
    private SocialBusinessPropertyService socialBusinessPropertyService;

    @Autowired
    private IBusinessCoreService iBusinessCoreService;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private GmbReportService gmbReportService;

    @Autowired
    private SocialPostService socialPostService;

    @Autowired
    FacebookExternalService facebookExternalService;
    @Autowired
    private SocialBusinessService socialBusinessService;

    @Autowired
    private ISocialPostsAssetService socialPostsAssetService;

    @Autowired
    private BusinessPostsAssetsRepo businessPostsAssetsRepo;
    @Autowired
    private IRedisExternalService redisExternalService;

    @Autowired
    private BusinessPagePictureAssetsRepo businessPagePictureAssetsRepo;
    @Autowired
    private KafkaProducerService kafkaProducerService;
    @Autowired
    private InstagramReportService instagramReportService;

    @Autowired
    private SocialTagService socialTagService;

    @Autowired
    private SocialPostInfoRepository socialPostInfoRepository;

    private static final String POST_LEVEL_INSIGHTS_TOPIC = "business-post-topic";
    private static final String SOCIAL_INSIGHT_AUDIT_TOPIC = "social-insight-audit";
    private static final String CDN_MIGRATION_ACC = "cdn-migration-acc";
    private static final String IG_BACKFILL_INSIGHT = "ig-backfill-insight";
    private static final String IN_PROGRESS = "in-progress";

    private static final String FORMAT_MM_DD_YYYY = "MMM dd yyyy";

    private final static Logger log = LoggerFactory.getLogger(SocialReportServiceImpl.class);

    @Autowired
    private SocialFBPageRepository fbPageRepository;

    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepository;

    @Autowired
    private BusinessLinkedinPageRepository linkedinPageRepository;

    @Autowired
    private SocialTwitterAccountRepository twitterAccountRepository;

    @Autowired
    private BusinessGMBLocationRawRepository gmbLocationRawRepository;

    @Autowired
    private BusinessYoutubeChannelRepository youtubeChannelRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private CronJobMetadataRepository cronJobMetadataRepository;



    @Override
    public Object getChannelPageInsights(InsightsRequest insightsRequest, String channel, Integer startIndex, Integer pageSize, String sortBy, Integer sortOrder) throws Exception {
        log.info("insightsRequest: {}, channel {} sortOrder {} sortBy {}", insightsRequest.toString(), channel, sortOrder, sortBy);
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
        populateRequestFields(insightsRequest, startIndex, pageSize, sortBy, sortOrder);
        return execute.getPageInsightsFromES(insightsRequest);
    }

    private void populateRequestFields(InsightsRequest insightsRequest, Integer startIndex, Integer pageSize, String sortBy, Integer sortOrder) {
        insightsRequest.setSortBy(sortBy);
        insightsRequest.setStartingIndex(Objects.nonNull(startIndex) ? startIndex : 0);
        insightsRequest.setSize(pageSize);
        insightsRequest.setSortingOrder(sortOrder);
    }

    @Override
    public Object getChannelPageInsightsForAll(InsightsRequest insightsRequest, Boolean type) throws Exception {
        log.info("getChannelPageInsightsForAll request: {}, channel {}", insightsRequest.toString(), insightsRequest.getSocialChannels());
        PageInsightsV2Response response = new PageInsightsV2Response();
        Map<String, CompletableFuture<PageInsightV2EsData>> map = new HashMap<>();
        Map<String, PageInsightV2EsData> data= new HashMap<>();
        int bucketSize = 0;
        List<String> channels = insightsRequest.getSocialChannels();
        if(CollectionUtils.isEmpty(channels)){
            channels = getAllSocialChannels();
        }
        if(type){
            for (String channel : channels) {
                try {
                    if(SocialChannel.YOUTUBE.getName().equals(channel)){
                        continue; // YouTube channel is not supported for profile performance.
                    }
                    String ch = SocialChannel.getSocialChannelByName(channel).getName();
                    SocialInsights execute = socialInsights.getSocialInsightsChannel(ch)
                            .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));

                    PageInsightV2EsData pageInsightsData = (PageInsightV2EsData) execute.getPageInsightsESData(insightsRequest);

                    data.put(channel, pageInsightsData);
                    log.info("Successfully retrieved data for channel {}", channel);

                } catch (Exception e) {
                    log.error("Exception while calling insights for channel {} and pageId {}", channel, insightsRequest.getBusinessIds(), e);
                }
            }
        }
        else {
            ExecutorService threadPool = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            for (String channel : channels) {
                if(SocialChannel.YOUTUBE.getName().equals(channel)){
                    continue; // YouTube channel is not supported for profile performance.
                }
                String ch = SocialChannel.getSocialChannelByName(channel).getName();
                SocialInsights execute = socialInsights
                        .getSocialInsightsChannel(ch)
                        .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));

                CompletableFuture<PageInsightV2EsData> requestCompletableFuture = CompletableFuture
                        .supplyAsync(
                                () ->
                                {
                                    try {
                                        return (PageInsightV2EsData) execute.getPageInsightsESData(insightsRequest);
                                    } catch (Exception e) {
                                        log.error("Exception while calling insights for channel {} and pageId {}", channel, insightsRequest.getBusinessIds());
                                    }
                                    return null;
                                }, threadPool);
                log.info("getChannelPageInsightsForAll channel {}", channel);
                if (Objects.isNull(requestCompletableFuture)) continue;
                map.put(channel, requestCompletableFuture);
            }
            data = allAsMap(map).get();
        }

        data.values().removeIf(Objects::isNull);
        bucketSize = getBucketSize(data.values(), bucketSize);

        response = reportsEsService.createResponseForPageInsight(data, insightsRequest, bucketSize);
        return response;
    }


    public static <U, T> CompletableFuture<Map<U, T>> allAsMap(
            Map<U, ? extends CompletionStage<? extends T>> map) {

        final List<U> keys = new ArrayList<>(map.keySet());
        @SuppressWarnings("unchecked") // generic array creation
        final CompletableFuture<? extends T>[] values = new CompletableFuture[keys.size()];
        for (int i = 0; i < keys.size(); i++) {
            values[i] = map.get(keys.get(i)).toCompletableFuture();
        }
        return CompletableFuture.allOf(values)
                .thenApply(
                        ignored -> {
                            final Map<U, T> result = new HashMap<>(values.length);
                            for (int i = 0; i < values.length; i++) {
                                result.put(keys.get(i), values[i].join());
                            }
                            return result;
                        });
    }


    private int getBucketSize(Collection<PageInsightV2EsData> insightV2EsDataCollection, Integer bucketSize) {
        for(PageInsightV2EsData insightsFromEs : insightV2EsDataCollection) {
            if (CollectionUtils.isNotEmpty(insightsFromEs.getMsgSentBuckets()) || CollectionUtils.isNotEmpty(insightsFromEs.getMsgReceivedBuckets())) {
                int sentBucketSize = CollectionUtils.isEmpty(insightsFromEs.getMsgSentBuckets()) ? 0 : insightsFromEs.getMsgSentBuckets().size();
                int receivedBucketSize = CollectionUtils.isEmpty(insightsFromEs.getMsgReceivedBuckets()) ? 0 : insightsFromEs.getMsgReceivedBuckets().size();
                int bucket = sentBucketSize > receivedBucketSize ? sentBucketSize : receivedBucketSize;
                if(bucket > bucketSize)
                    bucketSize = bucket;
            }

            if (CollectionUtils.isNotEmpty(insightsFromEs.getBuckets())) {
                if(insightsFromEs.getBuckets().size() > bucketSize)
                    bucketSize = insightsFromEs.getBuckets().size();
            }
        }
        return bucketSize;
    }

    private int getBucketSize(PageInsightV2EsData insightsFromEs, Integer bucketSize) {
        if (CollectionUtils.isNotEmpty(insightsFromEs.getMsgSentBuckets()) || CollectionUtils.isNotEmpty(insightsFromEs.getMsgReceivedBuckets())) {
            int sentBucketSize = CollectionUtils.isEmpty(insightsFromEs.getMsgSentBuckets()) ? 0 : insightsFromEs.getMsgSentBuckets().size();
            int receivedBucketSize = CollectionUtils.isEmpty(insightsFromEs.getMsgReceivedBuckets()) ? 0 : insightsFromEs.getMsgReceivedBuckets().size();
            int bucket = sentBucketSize > receivedBucketSize ? sentBucketSize : receivedBucketSize;
            if(bucket > bucketSize)
                bucketSize = bucket;
        }

        if (CollectionUtils.isNotEmpty(insightsFromEs.getBuckets())) {
            if(insightsFromEs.getBuckets().size() > bucketSize)
                bucketSize = insightsFromEs.getBuckets().size();
        }
        return bucketSize;
    }

    private List<String> getAllSocialChannels() {
        List<String> sourceIds = new ArrayList<>();
        sourceIds.add(SocialChannel.FACEBOOK.getName());
        sourceIds.add(SocialChannel.TWITTER.getName());
        sourceIds.add(SocialChannel.INSTAGRAM.getName());
        sourceIds.add(SocialChannel.LINKEDIN.getName());
        sourceIds.add(SocialChannel.YOUTUBE.getName());
        sourceIds.add(SocialChannel.TIKTOK.getName());
        return sourceIds;
    }

    private List<String> getAllSocialChannelsForSummaryExec() {
        List<String> sourceIds = new ArrayList<>();
        sourceIds.add(SocialChannel.FACEBOOK.getName());
        sourceIds.add(SocialChannel.TWITTER.getName());
        sourceIds.add(SocialChannel.INSTAGRAM.getName());
        sourceIds.add(SocialChannel.LINKEDIN.getName());
        sourceIds.add(SocialChannel.TIKTOK.getName());
        return sourceIds;
    }

    @Override
    public Object getChannelPageInsightsByLocation(InsightsRequest insightsRequest, String channel) throws Exception {
        log.info("insightsRequest: {}, channel {}", insightsRequest.toString(), channel);
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
        if(insightsRequest.getBusinessIds().size() > 1000) {
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "BusinessIds cannot be more than 1000");
        }
        return execute.getPageInsightsFromESByLocation(insightsRequest);
    }

    @Override
    public Object getChannelPostInsights(InsightsRequest insightsRequest, String channel, int startIndex, int pageSize, String sortParam,
                                         String sortOrder, boolean excelDownload) {
        log.info("insightsRequest: {}, channel {}", insightsRequest.toString(), channel);
        PostSortingCriteria postSortingCriteria = PostSortingCriteria.postSortingCriteria(sortParam);
        PostSortingOrder postSortingOrder = PostSortingOrder.postSortingOrder(sortOrder);
        if(postSortingCriteria == null || postSortingOrder == null) {
            throw new BadRequestException("Invalid sorting attributes");
        }
        String ch=null;
        try {
            ch = SocialChannel.getSocialChannelByName(channel).getName();
        } catch (Exception e) {
            if(ch == null) {
                if(channel.equals("all")) {
                    ch = "allChannels";
                }
            } else {
                throw e;
            }
        }

        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
        return execute.getPostDataAndInsightsFromES(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void postPageInsights(PageInsights pageInsights, String channel) {
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));

        execute.postPageInsightsToEs(pageInsights);
    }

    @Override
    public void getPostInsights(PostInsightsEventRequest request) {
        log.info("getPostInsights request received {}", request);
        List<BusinessPosts> businessPosts = businessPostsRepository.findByIdIn(request.getPostId());
        if (CollectionUtils.isNotEmpty(businessPosts)) {
            String ch = SocialChannel.getSocialChannelNameById(request.getSourceId());
            SocialInsights execute = socialInsights.getSocialInsightsChannel(ch)
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
            if (Objects.nonNull(request.getIsFreshRequestException()) && CollectionUtils.isNotEmpty(businessPosts) &&
                    request.getIsFreshRequestException().contains(businessPosts.get(0).getId())) {
                request.setIsFreshRequest(false);
            }
            execute.getPostInsights(businessPosts, request.getIsFreshRequest());
        } else {
            log.error("No Business Post Found with ids {} for fetching insights", request.getPostId());

        }
    }

    @Async
    @Override
    public void getPageInsightsForSocialChannel(SocialScanEventDTO socialScanEventDTO, String channel) {
        log.info("Get {} git {}",channel,socialScanEventDTO.getExternalId());
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
        try {
            if(Objects.nonNull(socialScanEventDTO.getEnterpriseId()) && !socialBusinessPropertyService.isSocialPostingAndReportingEnabled(socialScanEventDTO.getEnterpriseId())){
                log.info("Social Reporting is not enabled for enterprise id : {}", socialScanEventDTO.getEnterpriseId());
                return;
            }
            MDC.put("pageId",socialScanEventDTO.getExternalId());
            execute.getPageInsightsFromSocialChannel(socialScanEventDTO);
        }catch (BirdeyeSocialException ex){
            log.error("Exception while calling insights for channel {} and pageId {}", channel, socialScanEventDTO.getExternalId());
        }
    }

    @Override
    public void postPagePostToEs(PostData postData, String channel) {
        if(Objects.isNull(postData)) return;
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
        execute.postPostDataAndInsightsToEs(postData);

        if(Objects.nonNull(postData.getBePostId())) {
            Integer masterPostId = socialPostService.findMasterPostIdByPostId(Integer.valueOf(postData.getBePostId()));
            postData.setMasterPostId(masterPostId);
            commonService.sendPostInsights(postData);
        }
    }

    @Override
    public void updateTagInEs(SocialTagEntityMappingActionEvent tagUpdateEvent) {
        log.info("request received to update tags in es: {}", tagUpdateEvent);
        if(Objects.isNull(tagUpdateEvent) || Objects.isNull(tagUpdateEvent.getEntityId()) ||
                Objects.isNull(tagUpdateEvent.getAccountId()) ||
                Objects.isNull(tagUpdateEvent.getBusinessNumber()) ||
                !Objects.equals(tagUpdateEvent.getEntityType(), SocialTagEntityType.POST)) {
            log.info("invalid request");
            return ;
        }
        List<UpdateRequest> updateRequests = new ArrayList<>();
        List<SocialTagBasicDetail> basicDetailList = socialTagService.getBasicTagDetailForSingleEntityId(tagUpdateEvent.getEntityId(), SocialTagEntityType.POST);
        Set<Long> tagIds = CollectionUtils.isEmpty(basicDetailList)?null:
                basicDetailList.stream().map(SocialTagBasicDetail::getId).collect(Collectors.toSet());

        Map<String, Object> parameterMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(tagIds)) {
            parameterMap.put("tagIds", tagIds);
        } else {
            parameterMap.put("tagIds", null);
        }

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("be_post_id", String.valueOf(tagUpdateEvent.getEntityId())));
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(ElasticConstants.POST_INSIGHTS.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                //EsPostDataPoint esPostDataPoint = JSONUtils.fromJSON(hit.getSourceAsString(), EsPostDataPoint.class);
                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.id(hit.getId()).docAsUpsert(false)
                        .doc(JSONUtils.toJSON(parameterMap), XContentType.JSON)
                        .routing(String.valueOf(tagUpdateEvent.getBusinessNumber()))
                        .index(ElasticConstants.POST_INSIGHTS.getName());
                updateRequests.add(updateRequest);
            }

        }catch (IOException exception){
            log.info("Exception for Elastic search ",exception);
        }
        try {
            esService.updateDocumentInBulk(updateRequests);
        } catch (Exception e) {
            log.info("exception occurred while updating document in bulk for post id: {}, error: {}",
                    tagUpdateEvent.getEntityId(), e.getMessage());
        }
    }

    private List<List<Integer>> verifyTweetLimit(List<String> posts, List<Integer> ids) {
        List<List<Integer>> validTweets = new ArrayList<>();
        for(String p:posts) {
            List<Integer> postIds = Arrays.stream(p.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            if(postIds.size()> Constants.TWITTER_MAX_POSTS_INSIGHT) {
                postIds = postIds.subList(0,Constants.TWITTER_MAX_POSTS_INSIGHT);
            }
            ids.addAll(postIds);
            validTweets.add(postIds);
        }
        return validTweets;
    }

    @Override
    @Transactional
    public void dailySyncForPostInsights(String channel) {
        log.info("Received request for daily sync post insights for channel: {}", channel);
        Page<BusinessPosts> businessPosts;
        if(channel.equalsIgnoreCase(SocialChannel.TWITTER.getName())) {
            int twitterPostInsightLimit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).postInsightLimitTwitter();
            List<String> twitterPosts = businessPostsRepository.findTwitterPosts(DateUtils.addDays(new Date(), -1), twitterPostInsightLimit);
            if (CollectionUtils.isEmpty(twitterPosts)) {
                log.info("No data left for daily cron to pick from business_posts table for Twitter");
                return;
            }
            List<Integer> ids = new ArrayList<>();
            List<List<Integer>> twitterAccounts = verifyTweetLimit(twitterPosts, ids);
            businessPostsRepository.updateNextScanDateAndLastScanDateById(DateUtils.addDays(new Date(), 1), new Date(), ids);
            twitterAccounts.forEach(posts -> pushEventForPostInsights(channel, posts, null));
            return;
        }

        //For facebook, instagram and linkedin
        SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
        businessPosts = getBusinessPosts(socialChannel.getName(), socialChannel.getId());

        if (CollectionUtils.isEmpty(businessPosts.getContent())) {
            log.info("No data left for daily cron to pick from business_posts table for: {}", channel);
            return;
        }
        List<Integer> ids = businessPosts.getContent().stream().filter(b-> Objects.nonNull(b.getBusinessId()) && Objects.nonNull(b.getEnterpriseId())).map(BusinessPosts::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(ids)) {
            businessPostsRepository.updateNextScanDateAndLastScanDateById(DateUtils.addDays(new Date(), 1), new Date(), ids);
        }
        businessPosts.getContent().stream().filter(b-> Objects.nonNull(b.getBusinessId()) && Objects.nonNull(b.getEnterpriseId())).forEach(post -> pushEventForPostInsights(channel, Collections.singletonList(post.getId()), post.getPostEndDate()));
    }

    private Page<BusinessPosts> getBusinessPosts(String channel, Integer channelId) {
        Integer page = 0;
        Page<BusinessPosts> businessPosts;
        Integer postSize = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getIntegerProperty(SIZE_LIMIT_DAILY_SYNC.concat(".").concat(channel), 300); // postSize limit for each channel

        PageRequest pageRequest = new PageRequest(0, postSize, Sort.Direction.ASC, "lastScanDate"); // get top posts based on lastScanDate
        Date date = DateUtils.addDays(new Date(), -1);
        if(channel.equals(FACEBOOK)) {
            // via sync_business_posts the postId of FB is set as null, hence fetching only non-null records.
            // the postId is later on populated in savePagePosts method during social scan
            businessPosts = businessPostsRepository.findByLastScanDateLessThanAndSourceIdAndPostIdNotNull(date, channelId, pageRequest);
        } else {
            // For instagram and linkedin
            businessPosts = businessPostsRepository.findByLastScanDateLessThanAndSourceId(date, channelId, pageRequest);
        }
        return businessPosts;
    }

    private void pushEventForPostInsights(String channel, List<Integer> posts, Date postEndDate) {
        PostInsightsEventRequest eventRequest = new PostInsightsEventRequest();
        eventRequest.setSourceId(SocialChannel.getSocialChannelByName(channel).getId());
        eventRequest.setChannel(SocialChannel.getSocialChannelByName(channel).getName());
        eventRequest.setPostId(posts);
        eventRequest.setIsFreshRequest(false);

        if (channel.equalsIgnoreCase(SocialChannel.APPLE_CONNECT.getName())) {
            Date now = new Date();
            if (now.before(postEndDate) || now.equals(postEndDate)) {
                kafkaProducer.sendObjectV1(POST_LEVEL_INSIGHTS_TOPIC, eventRequest);
            }
        } else {
            kafkaProducer.sendObjectV1(POST_LEVEL_INSIGHTS_TOPIC, eventRequest);
        }
    }

    @Override
    public void insightSyncForIgStory() {
        List<Integer> intervalList = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getStoryInsightInterval();
        if(CollectionUtils.isNotEmpty(intervalList)) {
            for(Integer interval: intervalList) {
                List<Integer> businessPostIds = businessPostsRepository.findIgStoryInInterval(DateUtils.addHours(new Date(),-(interval+2)),
                        DateUtils.addHours(new Date(),-interval));
                if (CollectionUtils.isEmpty(businessPostIds)) {
                    log.info("No story found for daily cron to pick for mentioned interval from business_posts table: {}",interval);
                    continue;
                }
                businessPostIds.forEach(postId -> {
                    PostInsightsEventRequest eventRequest = new PostInsightsEventRequest();
                    eventRequest.setSourceId(SocialChannel.INSTAGRAM.getId());
                    eventRequest.setChannel(SocialChannel.INSTAGRAM.getName());
                    eventRequest.setPostId(Collections.singletonList(postId));
                    eventRequest.setIsFreshRequest(false);
                    kafkaProducer.sendObjectV1(POST_LEVEL_INSIGHTS_TOPIC, eventRequest);
                });
            }
        }
    }

    @Override
    public void updateAuditEvent(SocialInsightsAuditDto request) {
        try {
            if(Objects.nonNull(request.getId())) {
                SocialInsightsAudit socialInsightsAudit = insightsAuditRepository.findOne(request.getId());

                if(Objects.nonNull(socialInsightsAudit)) {
                    socialInsightsAudit.setStatus(request.getStatus());
                    socialInsightsAudit.setLastScannedOn(request.getLastScannedOn());

                    insightsAuditRepository.saveAndFlush(socialInsightsAudit);
                }
            } else {
                SocialInsightsAudit socialInsightsAudit = new SocialInsightsAudit();
                socialInsightsAudit.setBusinessId(request.getBusinessId());
                socialInsightsAudit.setPageId(request.getPageId());
                socialInsightsAudit.setPostId(request.getPostId());
                socialInsightsAudit.setStatus(request.getStatus());
                socialInsightsAudit.setLastScannedOn(request.getLastScannedOn());

                insightsAuditRepository.saveAndFlush(socialInsightsAudit);
            }

        } catch (Exception ex) {
            //add log
        }
    }


    @Async
    @Override
    public void initiatePostsFetch(String channel, SocialScanEventDTO data) {
        Optional<CronJobMetadata> cronJobMetadataOpt =
                cronJobMetadataRepository.findTopBySourceIdAndPageIdAndJobNameAndStatusOrderByCreatedAtDesc(data.getSourceId(),
                        data.getExternalId(), CronJobNameEnum.SOCIAL_POSTS_SCAN_JOB, CronJobStatusEnum.COMPLETED);
        try {
            if(Objects.nonNull(data.getEnterpriseId()) && !socialBusinessPropertyService.isSocialPostingAndReportingEnabled(data.getEnterpriseId())){
                log.info("[initiatePostsFetch] Social Reporting is not enabled for enterprise id : {}", data.getEnterpriseId());
                return;
            }
            SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
            ChannelPostService channelPostService = getPostService(socialChannel);

            // get last post date
            Date lastPostDate;
            if (cronJobMetadataOpt.isPresent()) {
                lastPostDate = cronJobMetadataOpt.get().getLastRunTime();
            } else {
                lastPostDate = DateUtils.addDays(new Date(), -(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialLastPostDays(socialChannel.getName().toLowerCase())));
            }

            // get posts or feeds via channel specific API from lastPostDate
            SocialTimeline socialTimeline = channelPostService.getFeedData(lastPostDate, data);
//            log.info("SocialTimeline data {}", socialTimeline);
            if(Objects.isNull(socialTimeline) || CollectionUtils.isEmpty(socialTimeline.getFeeds())) {
                log.info("No data fetched in social timeline for object: {}",data);
                return;
            }

            // dedupe to filter in new post ids
            Set<String> postIds = socialTimeline.getFeeds().stream().map(Feed::getFeedId).filter(Objects::nonNull).collect(Collectors.toSet());
            log.info("Social feed post ids : {}", postIds);
            if (CollectionUtils.isEmpty(postIds)) {
                log.info("No new posts found in postIds");
                return;
            }
            Map<String, String> targetIdVsPostId = getTargetIdVsPostIdMap(socialTimeline.getFeeds());
            // remove existing posts for all channels except fb as the existing posts must be saved via sync_business_post
            // and there postId needs to be updated.
            if (!(SocialChannel.FACEBOOK.getId() == data.getSourceId()) || !commonService.isBusinessAllowedToSyncBusinessPosts(data.getAccountId())) {
                dedupeService.removeExistingPosts(postIds);
            }
            //save data to business_post
            savePagePosts(socialTimeline, postIds, data, targetIdVsPostId);
            updateCronJobStatus(cronJobMetadataOpt, data, CronJobStatusEnum.COMPLETED);
        } catch (Exception ex) {
            updateCronJobStatus(cronJobMetadataOpt, data, CronJobStatusEnum.FAILED);
            log.info("Something went while saving business posts for event {} with error {}", data, ex);
        }

    }

    private void updateCronJobStatus(Optional<CronJobMetadata> cronJobMetadataOpt, SocialScanEventDTO data, CronJobStatusEnum status) {
        log.info("[updateCronJobStatus] updating cron job status for pageId: {} and sourceId: {} with status: {} for job: {}",
                data.getExternalId(), data.getSourceId(), status, CronJobNameEnum.SOCIAL_POSTS_SCAN_JOB);
        if (cronJobMetadataOpt.isPresent()) {
            CronJobMetadata cronJobMetadata = cronJobMetadataOpt.get();
            cronJobMetadata.setLastRunTime(new Date());
            if(CronJobStatusEnum.COMPLETED.equals(status)) {
                cronJobMetadata.setMetadata(CronJobMetadata.JobMetadata.builder().lastPostDate(new Date()).build());
            }
            cronJobMetadata.setStatus(status);
            cronJobMetadataRepository.saveAndFlush(cronJobMetadata);
        } else {
            CronJobMetadata cronJobMetadata = CronJobMetadata.builder()
                    .jobName(CronJobNameEnum.SOCIAL_POSTS_SCAN_JOB)
                    .lastRunTime(new Date())
                    .status(status)
                    .pageId(data.getExternalId())
                    .sourceId(data.getSourceId())
                    .rawTableId(data.getChannelPrimaryId())
                    .metadata(CronJobMetadata.JobMetadata.builder().lastPostDate(new Date()).build())
                    .build();

            cronJobMetadataRepository.saveAndFlush(cronJobMetadata);
        }
    }

    private Map<String, String> getTargetIdVsPostIdMap(List<Feed> feeds) {
        Map<String, String> targetIdvsPostId = new HashMap<>();
        ObjectMapper om = new ObjectMapper();
        for(Feed feed: feeds) {
            if(feed != null) {
                String targetId = feed.getTargetId();
                if(targetId != null) {
                    log.info("[getTargetIdVsPostIdMap] Adding in map key: {}, value:{}", targetId, feed.getFeedId());
                    targetIdvsPostId.put(targetId, feed.getFeedId());
                }
            }
        }
        return targetIdvsPostId;
    }

    @Override
    public void newPostFromBe(String channel, NewFbPostData newFbPostData) throws IOException {
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));

//        if(!socialBusinessService.isReportingEnabled(newFbPostData.getEnterpriseId())) return;
        execute.updateToPostAndPageIndexEs(newFbPostData);
    }

    @Override
    public void updatePageInsightsPostCount(PageInsights pageInsights) {
        String ch = pageInsights.getChannel();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));

        String indexName = execute.getPageInsightIndexName();
        reportsEsService.updatePageInsightsPostCount(pageInsights, indexName);
    }

    @Override
    public void removePage(List<ChannelPageRemoved> channelPageRemoved) {
        if(CollectionUtils.isEmpty(channelPageRemoved)){
            return;
        }
        channelPageRemoved.forEach(page -> {
            if(Objects.isNull(page.getPageId())){
                return;
            }
            SocialChannel socialChannel = SocialChannel.getSocialChannelByName(page.getChannelName());
            Integer sourceId = socialChannel.getId();
            // String index = socialChannel.getName()+"_"+ElasticConstants.PAGE_INSIGHTS.getName();
            log.info("Remove mapping for page id :{} in business posts table",page.getPageId());
            businessPostsRepository.updateBusinessIdWherePageId(page.getPageId(),sourceId,null,null);
        });
       /* removeMappingFromPageInsightsTable(channelPageRemoved.getProfileId(),socialChannel.getName(),null,null);
        LocationPageMappingRequest request = new LocationPageMappingRequest(channelPageRemoved.getBusinessId(),channelPageRemoved.getProfileId());
        updatePageInsightBusinessId(request,index,null,null);
        updatePostInsightBusinessId(request, SearchTemplate.POST_INSIGHTS.getName(),sourceId,null,null);*/
    }

    private void removeMappingFromPageInsightsTable(String pageId,String channel,Integer businessId,Long enterpriseId) {
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
        execute.updatePageInsightsDb(pageId,businessId,enterpriseId);
    }

    @Override
    public void removeMapping(List<LocationPageMappingRequest> locationPageMappingRequest,String channel) {
        log.info("Request initiated for remove mapping for page id :{}",locationPageMappingRequest.get(0).getPageId());
        if(CollectionUtils.isEmpty(locationPageMappingRequest) || StringUtils.isEmpty(channel)){
            log.info("No column can be updated by the request : {}",locationPageMappingRequest);
            return;
        }
        SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
        Integer sourceId = socialChannel.getId();
        //  String index = socialChannel.getName()+"_"+ElasticConstants.PAGE_INSIGHTS.getName();
        locationPageMappingRequest.forEach(page -> {
            log.info("Remove mapping for page id :{} in business posts table",page.getPageId());
            businessPostsRepository.updateBusinessIdWherePageId(page.getPageId(),sourceId,null,null);
           /* removeMappingFromPageInsightsTable(page.getProfileId(),socialChannel.getName(),null,null);
            updatePageInsightBusinessId(page,index,null,null);
            updatePostInsightBusinessId(page, SearchTemplate.POST_INSIGHTS.getName(),sourceId,null,null);*/
        });
    }

    @Override
    public void addMapping(LocationPageMappingRequest locationPageMappingRequest, String channel) {
        if(Objects.isNull(locationPageMappingRequest)){
            return;
        }

        SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
        Integer sourceId = socialChannel.getId();
        // String index = socialChannel.getName()+"_"+ElasticConstants.PAGE_INSIGHTS.getName();
        BusinessLiteDTO businessLiteDTO =  iBusinessCoreService.getBusinessLiteWithUpdated(locationPageMappingRequest.getLocationId());
        Long enterpriseId = null;
        if(Objects.nonNull(businessLiteDTO)){
            enterpriseId = businessLiteDTO.getEnterpriseNumber();
        }
        log.info("Add mapping for page id :{} in business posts table and enterprise id :{}",locationPageMappingRequest.getPageId(),businessLiteDTO.getEnterpriseId());
        Integer businessId = locationPageMappingRequest.getLocationId();
        businessPostsRepository.updateBusinessIdWherePageId(locationPageMappingRequest.getPageId(),sourceId,businessId,enterpriseId);

        publishToKafkaForPageScan(locationPageMappingRequest.getPageId(), channel);
       /* removeMappingFromPageInsightsTable(locationPageMappingRequest.getProfileId(),socialChannel.getName(),businessId,enterpriseId);
        updatePageInsightBusinessId(locationPageMappingRequest,index,businessId,enterpriseId);
        updatePostInsightBusinessId(locationPageMappingRequest, SearchTemplate.POST_INSIGHTS.getName(),sourceId,businessId,enterpriseId);*/
    }

    private void publishToKafkaForPageScan(String pageId, String channel) {
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));

        execute.startScanForPosts(pageId);
    }

    @Async
    void updatePostInsightBusinessId(LocationPageMappingRequest page, String index,Integer sourceId,Integer businessId,Long enterpriseId) {
        try {
            List<EsPostDataPoint> esPostDataPoints = reportsEsService.getPostDataForPageId(page.getPageId(),null,index,sourceId);
            if(CollectionUtils.isNotEmpty(esPostDataPoints)){
                esPostDataPoints.forEach(postDataPoint -> {
                    postDataPoint.setBusiness_id(businessId);
                    postDataPoint.setEnt_id(enterpriseId);
                });
                reportsEsService.bulkPostPagePostDataToES(esPostDataPoints,index);
            }
        } catch (IOException e) {
            log.info("IOException exception occurred while getting data from es for page id : {}",page.getPageId());
        }
    }

    @Async
    void updatePageInsightBusinessId(LocationPageMappingRequest page, String index,Integer businessId,Long enterpriseId){
        try {
            List<ESPageRequest> pageRequests = reportsEsService.getPageDataForPageId(page.getPageId(),null,null,index);
            if(CollectionUtils.isNotEmpty(pageRequests)){
                pageRequests.forEach(pageRequest -> {
                    pageRequest.setBusiness_id(businessId);
                    pageRequest.setEnt_id(enterpriseId);
                });
                reportsEsService.bulkPostPageInsights(pageRequests,index);
            }
        } catch (IOException e) {
            log.info("IOException exception occurred while getting data from es for page id : {}",page.getPageId());
        }
    }


    public ChannelPostService getPostService(SocialChannel socialChannel) {
        return ChannelPostServiceFactory.getService(socialChannel);
    }

    private void savePagePosts(SocialTimeline socialTimeline, Set<String> newPostIds, SocialScanEventDTO dto, Map<String, String> targetIdVsPostId) {
        List<BusinessPosts> response = new ArrayList<>();
        try {
            log.info("Saving posts to db: {}", newPostIds);
            // TODO @saurabh -- clarification get Map<newPostId, be_post_id> in 1 db query where channel = <> & date > lastpublish
            // this query should run on postsIds, date & channel check is not understood.
            Map<String, Integer> extPostIdToBePostId = new HashMap<>();

            extPostIdToBePostId = dedupeService.freshPostsCheckInSystem(newPostIds, targetIdVsPostId);
            if (Objects.isNull(extPostIdToBePostId)) {
                log.info("No poss found with postIds {} in business posts", newPostIds);
            }

            Set<Integer> isExistingBusinessPost = new HashSet<>();
            for (Feed feed : socialTimeline.getFeeds()) {
                if (!newPostIds.contains(feed.getFeedId())) {
                    continue;
                }
                try {
                    Integer bePostId = Objects.isNull(extPostIdToBePostId) ? null : extPostIdToBePostId.get(feed.getFeedId());
                    if (Objects.nonNull(bePostId)) {
                        log.info("[savePagePosts] bePostId: {} and businessId: {}", bePostId, dto.getBusinessId());
                        if (commonService.isBusinessAllowedToSyncBusinessPosts(dto.getAccountId())) {
                            if(SocialChannel.FACEBOOK.getId() == dto.getSourceId()) {
                                List<BusinessPosts> businessPosts = businessPostsRepository.findByBePostIdAndExternalPageId(bePostId, dto.getExternalId());
                                BusinessPosts bp;
                                Optional<BusinessPosts> matchingBusinessPost = findMatchingBusinessPost(businessPosts, feed);
                                if (matchingBusinessPost.isPresent()) {
                                    bp = matchingBusinessPost.get();
                                    log.info("[savePagePosts] matching busines post found: {}", bp.toString());
                                    if (Objects.isNull(bp.getPostId())) {
                                        // updating postId of already saved business_posts through sync_businessPosts for facebook
                                        log.info("updating postId for business post with id: {} with feedId: {}", bp.getId(), feed.getFeedId());
                                        bp.setPostId(feed.getFeedId());
                                        // delete the doc created during sync business post where postId does not exist
                                        esService.deleteDocumentByBePostIdAndPageId("post_insight", bePostId, dto.getExternalId());
                                        //set mediaId for reel which were created through sync post
                                        if (feed.isReel()) {
                                            BusinessPostMetadata businessPostMetadata = new BusinessPostMetadata();
                                            if (StringUtils.isNotEmpty(bp.getMetadata())) {
                                                businessPostMetadata = JSONUtils.fromJSON(bp.getMetadata(), BusinessPostMetadata.class);
                                            }
                                            log.info("Updating media for reel: {}, mediaId: {}", bp.getPostId(), feed.getMediaId());
                                            if (StringUtils.isNotEmpty(feed.getMediaId())) {
                                                businessPostMetadata.setMediaId(feed.getMediaId());
                                                bp.setMetadata(JSONUtils.toJSON(businessPostMetadata));
                                            }
                                        }
                                        // setting createdByBeUserId so that createdByBeUser details is again
                                        // calculated when new data is indexed
                                        bp.setCreatedByBeUserId(null);
                                        BusinessPosts savedBusinessPosts = businessPostsRepository.saveAndFlush(bp);
                                        response.add(savedBusinessPosts);
                                        isExistingBusinessPost.add(savedBusinessPosts.getId());
                                    }
                                } else {
                                    log.info("[savePagePosts] no match found");
                                }
                            }
                            // Businesses that are restricted from syncing business posts should
                            // still be saved in the businessPosts table through this process.
                        } else {
                            log.info("saving data in business posts for bePostId: {} and feedId: {} for channel: {}",
                                    bePostId, feed.getFeedId(), dto.getSourceId());
                            BusinessPosts businessPost = ConversionUtils.conversionToBusinessPosts(feed, dto, bePostId);
                            response.add(businessPostsRepository.saveAndFlush(businessPost));
                        }
                    } else {
                        // non birdeye posts saved in businessPost table
                        BusinessPosts businessPost = ConversionUtils.conversionToBusinessPosts(feed, dto, bePostId);
                        response.add(businessPostsRepository.saveAndFlush(businessPost));
                    }
                } catch (Exception ex) {
                    log.info("Something went wrong while saving feed data to business posts {} with error", feed, ex);
                }
            }

            if (CollectionUtils.isNotEmpty(response) && response.get(0).getSourceId() == SocialChannel.TWITTER.getId()) {
                insightsEventTriggerAcknowledgement(response, isExistingBusinessPost);
            } else {
                response.forEach(res -> insightsEventTriggerAcknowledgement(Collections.singletonList(res), isExistingBusinessPost));
            }
        } catch (Exception ex) {
            log.info("[Social Report] Something went wrong while saving for businessPosts for fb post with error {}", socialTimeline, ex);
        }
        //return response;
    }

    private Optional<BusinessPosts> findMatchingBusinessPost(List<BusinessPosts> businessPosts, Feed feed) {
        String targetId = feed.getTargetId() != null ? feed.getTargetId() : feed.getFeedId();

        return businessPosts.stream()
                .filter(post -> isMatchingBusinessPost(post, targetId))
                .findFirst();
    }

    private boolean isMatchingBusinessPost(BusinessPosts post, String targetId) {
        if (post.getMetadata() != null) {
            try {
                BusinessPostMetadata metadata = JSONUtils.fromJSON(post.getMetadata(), BusinessPostMetadata.class);
                return metadata != null && targetId.equals(metadata.getTargetId());
            } catch (Exception e) {
                log.error("Error parsing meta_data for businessPostId: {}", post.getId(), e);
            }
        }
        return false;
    }


    private boolean insightsEventTriggerAcknowledgement(List<BusinessPosts> posts, Set<Integer> isExistingBusinessPost) {
        try {
            PostInsightsEventRequest eventRequest = new PostInsightsEventRequest();
            eventRequest.setSourceId(posts.get(0).getSourceId());
            eventRequest.setChannel(SocialChannel.getSocialChannelNameById(eventRequest.getSourceId()));
            List<Integer> postIds = posts.stream().map(BusinessPosts::getId).collect(Collectors.toList());
            eventRequest.setPostId(postIds);
            eventRequest.setIsFreshRequestException(isExistingBusinessPost);
            log.info("Triggering post insight fetch for payload: {}",eventRequest);
            kafkaProducer.sendObjectV1(POST_LEVEL_INSIGHTS_TOPIC, eventRequest);
            return true;
        } catch (Exception ex) {
            return false;
        }

    }

    private void insightsAuditEvent(Integer businessId, String status, Date lastScannedOn, String pageId, String postId) {
        SocialInsightsAuditDto insightsAuditDto = new SocialInsightsAuditDto();
        insightsAuditDto.setBusinessId(businessId);
        insightsAuditDto.setStatus(status);
        insightsAuditDto.setLastScannedOn(lastScannedOn);
        insightsAuditDto.setPageId(pageId);
        insightsAuditDto.setPostId(postId);

        kafkaProducer.sendObjectV1(SOCIAL_INSIGHT_AUDIT_TOPIC, insightsAuditDto);
    }


    // TODO @saurabh -fixed should be in conversion utils
    // moved to conversion utils

    @Override
    public PageFollowersData getFollowersCountEs(InsightsRequest insights) throws Exception {
        log.info("Request to get insight for pages from business ids with enterprise id: {}", insights.getEnterpriseId());
        List<Integer> businessIds=insights.getBusinessIds();

        if (businessIds == null || businessIds.isEmpty()) {

            throw new BadRequestException("Empty BusinessIds in request");
        }

        return reportsEsService.getFollowersResponse(insights);

    }



    @Override
    public void populateBEPostId(int days,int size,boolean limit) {
        Date to = new Date();
        Date from = DateUtils.addDays(to,-days);
        int page = 0;
        AtomicInteger failureCases = new AtomicInteger();
        int notProcessedCases = 0;
        int sum = 0;
        AtomicInteger processedCases = new AtomicInteger();
        log.info("Data from social post publish info table for date b/w from : {} , to:{}",from,to);
        do {
            // post containing only video or photo , isPublished = 1
            if(limit && page == 1){
                break;
            }
            List<PagePostData> pagePostsData = getPublishedData(page,size);
            log.info("Page : {} ,size : {}",page,size);
            //6348
            if(CollectionUtils.isEmpty(pagePostsData)){
                return;
            }
            pagePostsData.stream().filter(post -> StringUtils.isNotEmpty(post.getPostId()) && !post.getPostId().contains("_")).forEach( data->{
                log.info("Prepare postId from promotableId for business posts table :{}",data.getPostId());
                try {
                    String[] promotableId = facebookExternalService.
                            getPromotableId(data.getPostId(),data.getPageAccessToken(),data.getExternalPageId()).split("_");
                    String postId = data.getExternalPageId()+"_"+promotableId[1];
                    processedCases.incrementAndGet();
                    businessPostsRepository.updatePostIdWhereId(postId,data.getId());
                }catch (Exception e){
                    failureCases.incrementAndGet();
                    log.info("Error while fetching post : {}",e.getMessage());
                }
            });
            log.info("Find postIds from business posts table size : {}",pagePostsData.size());
            notProcessedCases = notProcessedCases + (pagePostsData.size() - processedCases.get());
            sum = sum + processedCases.get() + notProcessedCases + failureCases.get();
            log.info("Processed cases : {}, notProcessed cases : {} , failure cases : {} , total : {}", processedCases.get(),notProcessedCases,failureCases,sum);
            page++;
        }while (true);
    }

    private List<PagePostData> getPublishedData(int page, int size) {
        List<Object[]> dataList =  businessPostsRepository.getDataForVideoAndPhoto(new PageRequest(page,size));
        List<PagePostData> pagePostDataList = new ArrayList<>();
        if(CollectionUtils.isEmpty(dataList)){
            return pagePostDataList;
        }
        dataList.forEach(data -> {
            PagePostData pagePostData = new PagePostData();
            pagePostData.setId((Integer) data[0]);
            pagePostData.setExternalPageId((String) data[1]);
            pagePostData.setPostId((String) data[2]);
            pagePostData.setPageAccessToken((String) data[3]);
            pagePostDataList.add(pagePostData);
        });
        return pagePostDataList;
    }



    @Override
    public void getGMBPageAnalytics(Integer businessId, String pageId,String channel) throws Exception {
        log.info("Get GMB analytics for page id : {} and businessId : {}", pageId, businessId);
        try {
            String ch = SocialChannel.getSocialChannelByName(channel).getName();
            SocialInsights execute = socialInsights.getSocialInsightsChannel(ch)
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
            execute.getGMBPageAnalytics(pageId, businessId);
        } catch (BirdeyeSocialException ex) {
            log.error("Exception while calling analytics for page id : {} and businessId : {}", pageId, businessId);
            throw new BirdeyeSocialException("[Birdeye Exception]",ex.getCause());
        }

    }

    @Override
    public void getGMBKeywordAnalytics(Integer businessId, String channel) throws Exception {
        log.info("Get GMB analytics for businessId : {}", businessId);
        try {
            String ch = SocialChannel.getSocialChannelByName(channel).getName();
            SocialInsights execute = socialInsights.getSocialInsightsChannel(ch)
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
            execute.getGMBKeywordAnalytics(businessId);
        } catch (BirdeyeSocialException ex) {
            log.error("Exception while calling analytics for page id : {} and businessId : {}", businessId);
            throw new BirdeyeSocialException("[Birdeye Exception]",ex.getCause());
        }
    }

    private void saveCDNPostToES(BusinessPosts businessPost) {
        String ch = SocialChannel.getSocialChannelNameById(businessPost.getSourceId());
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
        execute.saveCDNPostToES(businessPost);

    }
    @Override
    public void consumeCDNUploadEvent(CDNEventRequest eventRequest) {
        log.info("Request received to update cdn url for payload: {}",eventRequest);
        try {
            if (Objects.nonNull(eventRequest) && Objects.nonNull(eventRequest.getSourceKey())) {
                BusinessPostsAssets businessPostsAssets = businessPostsAssetsRepo.findOne(Integer.valueOf(eventRequest.getSourceKey()));
                if (Objects.isNull(businessPostsAssets)) {
                    log.info("No entry found for given id: {}", eventRequest.getSourceKey());
                    return;
                }
                BusinessPosts businessPosts = businessPostsRepository.findById(businessPostsAssets.getBusinessPostsId());
                if(eventRequest.getStatus().equals("success")) {
                    String cdnBaseUrl = socialPostsAssetService.getBaseCdnUrl();
                    String cdnUrl = cdnBaseUrl + "/" + eventRequest.getSubBucket() + "/" + eventRequest.getCdnUrl();
                    businessPostsAssets.setCdnUrl(cdnUrl);
                    businessPostsAssetsRepo.saveAndFlush(businessPostsAssets);
                    String imageUrl = businessPosts.getImageUrls();
                    imageUrl = imageUrl.replace(businessPostsAssets.getMediaUrl(), cdnUrl);
                    businessPosts.setImageUrls(imageUrl);
                    businessPostsRepository.saveAndFlush(businessPosts);
                } else {
                    businessPostsAssets.setCdnUrl(eventRequest.getFailureReason());
                    businessPostsAssetsRepo.saveAndFlush(businessPostsAssets);
                }

                Optional<Object> redisValue = redisExternalService.get(String.valueOf(businessPosts.getId()));
                if (redisValue.isPresent()) {
                    List<Integer> ids = (List<Integer>) redisValue.get();
                    ids.remove(businessPostsAssets.getId());
                    if (CollectionUtils.isEmpty(ids)) {
                        redisExternalService.delete(String.valueOf(businessPosts.getId()));
                        saveCDNPostToES(businessPosts);
                    } else {
                        redisExternalService.delete(String.valueOf(businessPosts.getId()));
                        redisExternalService.set(String.valueOf(businessPosts.getId()), ids);
                    }
                }
            }
        } catch (Exception e) {
            log.info("Exception while updating cdn url for id: {}",eventRequest.getSourceKey(),e);
        }
    }

    @Override
    public void consumeCDNUploadEventForPage(CDNEventRequest eventRequest) {
        log.info("Request received to update cdn url for payload: {}",eventRequest);
        try {
            if(Objects.nonNull(eventRequest) && StringUtils.isNotEmpty(eventRequest.getStatusCode()) && !eventRequest.getStatusCode().equals("200")) {
                log.info("cdn upload failed for key: {} because of : {}", eventRequest.getSourceKey(),eventRequest.getFailureReason());
                return;
            }
            if (Objects.nonNull(eventRequest) && Objects.nonNull(eventRequest.getSourceKey())) {
                String[] keySap = eventRequest.getSourceKey().split("_");
                if(keySap.length!=2) {
                    log.info("No entry found for given id: {}", eventRequest.getSourceKey());
                    return;
                }
                Integer pageId = Integer.parseInt(keySap[1]);
                String channel = keySap[0];

                String cdnBaseUrl = socialPostsAssetService.getBaseCdnUrl();
                String cdnUrl = cdnBaseUrl + "/" + eventRequest.getSubBucket() + "/" + eventRequest.getCdnUrl();

                if (SocialChannel.FACEBOOK.getName().equals(channel)) {
                    BusinessFBPage facebookPage = fbPageRepository.findById(pageId);
                    if (Objects.nonNull(facebookPage)) {
                        facebookPage.setFacebookPagePictureUrl(cdnUrl);
                        fbPageRepository.saveAndFlush(facebookPage);
                    }
                } else if (SocialChannel.TWITTER.getName().equals(channel)) {
                    BusinessTwitterAccounts twitterAccount = twitterAccountRepository.findById(pageId);
                    if (Objects.nonNull(twitterAccount)) {
                        twitterAccount.setProfilePicUrl(cdnUrl);
                        twitterAccountRepository.saveAndFlush(twitterAccount);
                    }
                } else if (SocialChannel.GOOGLE.getName().equals(channel)) {
                    BusinessGoogleMyBusinessLocation googleMyBusinessLocation = gmbLocationRawRepository.findById(pageId);
                    if (Objects.nonNull(googleMyBusinessLocation)) {
                        googleMyBusinessLocation.setPictureUrl(cdnUrl);
                        gmbLocationRawRepository.saveAndFlush(googleMyBusinessLocation);
                    }
                } else if (SocialChannel.INSTAGRAM.getName().equals(channel)) {
                    BusinessInstagramAccount instagramAccount = instagramAccountRepository.findById(pageId);
                    if (Objects.nonNull(instagramAccount)) {
                        instagramAccount.setInstagramAccountPictureUrl(cdnUrl);
                        instagramAccountRepository.saveAndFlush(instagramAccount);
                    }
                } else if (SocialChannel.LINKEDIN.getName().equals(channel)) {
                    BusinessLinkedinPage linkedinPage = linkedinPageRepository.findById(pageId);
                    if (Objects.nonNull(linkedinPage)) {
                        linkedinPage.setLogoUrl(cdnUrl);
                        linkedinPageRepository.saveAndFlush(linkedinPage);
                    }
                } else if (SocialChannel.YOUTUBE.getName().equals(channel)) {
                    BusinessYoutubeChannel youtubeChannel = youtubeChannelRepository.findById(pageId);
                    if (Objects.nonNull(youtubeChannel)) {
                        youtubeChannel.setPictureUrl(cdnUrl);
                        youtubeChannelRepository.saveAndFlush(youtubeChannel);
                    }
                } else if (SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)||SocialChannel.WHATSAPP.getName().equalsIgnoreCase(channel)) {
                    ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
                    arborService.updateProfileImageCDN(pageId, cdnUrl);
                }
            }
        } catch (Exception e) {
            log.info("Exception while updating cdn url for id: {}",eventRequest.getSourceKey(),e);
        }
    }


    @Override
    public void cdnMigration(int days,int src) {
        Date from = DateUtils.addDays(new Date(),-days);
        List<String> externalIds = businessPostsRepository.findExternalIds(from,src);
        log.info("[cdn Migration] No. of accounts fetched for sourceId: {}: {}",src,externalIds.size());
        for(String externalId : externalIds) {
            CDNMigrationAccount migrationAccount = new CDNMigrationAccount();
            migrationAccount.setExternalId(externalId);
            migrationAccount.setSource(src);
            migrationAccount.setLastDate(from);
            kafkaProducer.sendObjectV1(CDN_MIGRATION_ACC,migrationAccount);
        }
    }

    private Integer getChannelPrimaryId(CDNMigrationAccount migrationAccount) {
        Integer id = null;
        Integer src = migrationAccount.getSource();
        switch (src) {
            case 108:
                BusinessTwitterAccounts twitterAccounts = twitterAccountRepository.findFirstByProfileId(Long.parseLong(migrationAccount.getExternalId()));
                id = twitterAccounts.getId();
                break;

            case 109:
                BusinessLinkedinPage linkedinPage = linkedinPageRepository.findFirstByProfileId(migrationAccount.getExternalId());
                id = linkedinPage.getId();
                break;

            case 110:
                BusinessFBPage fbPage = fbPageRepository.findFirstByFacebookPageId(migrationAccount.getExternalId());
                id = fbPage.getId();
                break;

            case 195:
                BusinessInstagramAccount instagramAccount = instagramAccountRepository.findByInstagramAccountId(migrationAccount.getExternalId());
                id = instagramAccount.getId();
                break;
            default:
                log.info("No valid source found!!");
        }
        return id;
    }

    @Override
    public void postFetch(CDNMigrationAccount migrationAccount) {
        log.info("[cdn Migration] Fetching posts for account: {}",migrationAccount);
        if(Objects.isNull(migrationAccount) || Objects.isNull(migrationAccount.getExternalId()) || Objects.isNull(migrationAccount.getSource())) {
            log.info("[cdn Migration] Invalid request, returning!!");
            return;
        }
        SocialChannel socialChannel = SocialChannel.getSocialChannelByName(SocialChannel.getSocialChannelNameById(migrationAccount.getSource()));
        ChannelPostService channelPostService = getPostService(socialChannel);
        Integer primaryId = getChannelPrimaryId(migrationAccount);
        if(Objects.isNull(primaryId)) {
            log.info("[cdn Migration] Cannot find channel primary ID!!");
            return;
        }
        SocialScanEventDTO data = new SocialScanEventDTO();
        data.setChannelPrimaryId(primaryId);
        SocialTimeline socialTimeline = channelPostService.getFeedData(migrationAccount.getLastDate(), data);
        if(Objects.nonNull(socialTimeline) && CollectionUtils.isNotEmpty(socialTimeline.getFeeds())) {
            log.info("[cdn Migration] Fetched posts for account: {}, size: {}",migrationAccount.getExternalId(),socialTimeline.getFeeds().size());
            processFetchedPosts(socialTimeline, migrationAccount.getSource(),migrationAccount.getLastDate(),migrationAccount.getExternalId());
        }
    }

    private void processFetchedPosts(SocialTimeline socialTimeline, Integer src, Date lastDate, String externalId) {
        List<BusinessPosts> businessPosts = businessPostsRepository.findBySourceIdAndExternalPageIdAndPublishDateGreaterThan(src,externalId,lastDate);
        for(Feed feed: socialTimeline.getFeeds()) {
            if(CollectionUtils.isNotEmpty(feed.getImages())) {
                BusinessPosts businessPost = businessPosts.stream().filter(bp->bp.getPostId().equalsIgnoreCase(feed.getFeedId())).findFirst().orElse(null);
                if (Objects.nonNull(businessPost)) {
                    log.info("[cdn Migration] Processing for account: {}",socialTimeline.getPageId());
                    businessPost.setImageUrls(String.join(",", feed.getImages()));
                    businessPostsRepository.saveAndFlush(businessPost);
                    commonService.migrateImageToCDN(businessPost.getId(),feed.getImages());
                }
            }
        }
    }

    @Override
    public void consumeCDNMigrateEvent(CDNEventRequest eventRequest) {
        log.info("Request received to update cdn url for payload: {}",eventRequest);
        try {
            if (Objects.nonNull(eventRequest) && Objects.nonNull(eventRequest.getSourceKey())) {
                BusinessPostsAssets businessPostsAssets = businessPostsAssetsRepo.findOne(Integer.valueOf(eventRequest.getSourceKey()));
                if (Objects.isNull(businessPostsAssets)) {
                    log.info("No entry found for given id: {}", eventRequest.getSourceKey());
                    return;
                }
                BusinessPosts businessPosts = businessPostsRepository.findById(businessPostsAssets.getBusinessPostsId());
                if(eventRequest.getStatus().equals("success")) {
                    String cdnBaseUrl = socialPostsAssetService.getBaseCdnUrl();
                    String cdnUrl = cdnBaseUrl + "/" + eventRequest.getSubBucket() + "/" + eventRequest.getCdnUrl();
                    businessPostsAssets.setCdnUrl(cdnUrl);
                    businessPostsAssetsRepo.saveAndFlush(businessPostsAssets);
                    String imageUrl = businessPosts.getImageUrls();
                    imageUrl = imageUrl.replace(businessPostsAssets.getMediaUrl(), cdnUrl);
                    businessPosts.setImageUrls(imageUrl);
                    businessPostsRepository.saveAndFlush(businessPosts);
                } else {
                    businessPostsAssets.setCdnUrl(eventRequest.getFailureReason());
                    businessPostsAssetsRepo.saveAndFlush(businessPostsAssets);
                }

                Optional<Object> redisValue = redisExternalService.get(String.valueOf(businessPosts.getId()));
                if (redisValue.isPresent()) {
                    List<Integer> ids = (List<Integer>) redisValue.get();
                    ids.remove(businessPostsAssets.getId());
                    if (CollectionUtils.isEmpty(ids)) {
                        log.info("[cdn Migration] Updating es for businessPostId: {}",businessPosts.getId());
                        redisExternalService.delete(String.valueOf(businessPosts.getId()));
                        List<String> images = Arrays.asList(businessPosts.getImageUrls().split(","));
                        esService.updateMigratedData("post_insight",businessPosts.getPostId(),images);
                    } else {
                        redisExternalService.delete(String.valueOf(businessPosts.getId()));
                        redisExternalService.set(String.valueOf(businessPosts.getId()), ids);
                    }
                }
            }
        } catch (Exception e) {
            log.info("Exception while updating cdn url for id: {}",eventRequest.getSourceKey(),e);
        }
    }

    private PostInsightDTO convertIgDataToPostInsightData(InstagramValue instagramValue) {
        PostInsightDTO postInsightDTO = new PostInsightDTO();
        postInsightDTO.setReach(instagramValue.getReach());
        postInsightDTO.setImpression(instagramValue.getImpressions());
        return postInsightDTO;
    }

    private PostData createPostData(BusinessPosts businessPosts, PostInsightDTO postInsightDTO) {
        List<String> media;
        PostData postData = new PostData();
        postData.setId(businessPosts.getId());
        postData.setBePostId(businessPosts.getBePostId()!=null?businessPosts.getBePostId().toString()
                :null);
        postData.setPostId(businessPosts.getPostId());
        postData.setSourceId(businessPosts.getSourceId());
        postData.setPageName(businessPosts.getPageName());
        postData.setPageId(businessPosts.getExternalPageId());
        postData.setBusinessId(businessPosts.getBusinessId());
        postData.setEnterpriseId(businessPosts.getEnterpriseId());
        postData.setPostUrl(businessPosts.getPostUrl());
        postData.setPostText(businessPosts.getPostText());
        postData.setPostingDate(businessPosts.getPublishDate());
        if(StringUtils.isNotEmpty(businessPosts.getImageUrls())) {
            media = Arrays.asList(businessPosts.getImageUrls().split(","));
            postData.setImages(media);
        }
        if( StringUtils.isNotEmpty(businessPosts.getVideoUrls())) {
            media = Arrays.asList(businessPosts.getVideoUrls().split(","));
            postData.setVideos(media);
        }
        postData.setEngagement(postInsightDTO.getEngagement());
        postData.setImpression(postInsightDTO.getImpression());
        postData.setReach(postInsightDTO.getReach());
        if(Objects.nonNull(postData.getEngagement()) && Objects.nonNull(postData.getReach()) && postData.getReach()!=0) {
            postData.setEngagementRate((double)postData.getEngagement()/postData.getReach());
        }

        return postData;
    }
    @Override
    public void consumeIGStoryInsightEvent(InstagramEventRequest instagramEventRequest) {

        log.info("Ig story insight event payload: {}",instagramEventRequest);
        try {
            if (CollectionUtils.isNotEmpty(instagramEventRequest.getEntry()) && Objects.nonNull(instagramEventRequest.getEntry().get(0).getId())) {
                InstagramEntry igEntry = instagramEventRequest.getEntry().get(0);
                if (CollectionUtils.isNotEmpty(igEntry.getChanges()) && Objects.nonNull(igEntry.getChanges().get(0).getValue())
                        && igEntry.getChanges().get(0).getField().equalsIgnoreCase("story_insights")) {
                    InstagramValue instagramValue = igEntry.getChanges().get(0).getValue();
                    if(instagramValue.getReach()==-1) {
                        log.info("Reach is -1 for payload: {}",instagramEventRequest);
                        return;
                    }
                    BusinessPosts businessPost = businessPostsRepository.findByPostIdAndSourceId(instagramValue.getMedia_id(),SocialChannel.INSTAGRAM.getId());
                    if(Objects.nonNull(businessPost)) {
                        PostInsightDTO postInsightDTO = convertIgDataToPostInsightData(instagramValue);
                        PostData postData = createPostData(businessPost, postInsightDTO);
                        kafkaProducerService.sendObjectV1(InsightsConstants.IG_POST_INSIGHT_TOPIC, postData);
                    }
                }
            }
        } catch (Exception e) {
            log.info("Error updating story insight for payload: {}",instagramEventRequest,e);
        }
    }

    @Override
    public void refreshIgPosts(IgStoryEventRequest igStoryEventRequest) {
        BusinessInstagramAccount businessInstagramAccount = instagramAccountRepository.findByInstagramAccountId(igStoryEventRequest.getIgAccountId());
        if(Objects.isNull(businessInstagramAccount)) {
            log.info("No account found with id: {}",igStoryEventRequest.getIgAccountId());
            return;
        }
        log.info("refreshing IG posts manually for accountId: {}",igStoryEventRequest.getIgAccountId());
        Date eligibleDate = DateUtils.addMinutes(new Date(),-15);
        if(Objects.isNull(businessInstagramAccount.getManualScannedOn())||businessInstagramAccount.getManualScannedOn().before(eligibleDate)) {
            businessInstagramAccount.setManualScannedOn(new Date());
            instagramAccountRepository.saveAndFlush(businessInstagramAccount);
            SocialScanEventDTO socialScanEventDTO = new SocialScanEventDTO();
            socialScanEventDTO.setChannelPrimaryId(businessInstagramAccount.getId());
            socialScanEventDTO.setExternalId(businessInstagramAccount.getInstagramAccountId());
            socialScanEventDTO.setPageName(businessInstagramAccount.getInstagramAccountName());
            socialScanEventDTO.setBusinessId(businessInstagramAccount.getBusinessId());
            socialScanEventDTO.setEnterpriseId(businessInstagramAccount.getEnterpriseId());
            socialScanEventDTO.setSourceId(SocialChannel.INSTAGRAM.getId());
            socialScanEventDTO.setSourceName(SocialChannel.INSTAGRAM.getName());
            initiatePostsFetch(SocialChannel.INSTAGRAM.getName(),socialScanEventDTO);
        }
    }

    @Override
    public PerformanceSummaryResponse getChannelSummaryForAllChannel(InsightsRequest insightsRequest) throws Exception {
        PerformanceSummaryResponse response = new PerformanceSummaryResponse();

        log.info("getChannelSummaryForAllChannel request: {}, channel {}", insightsRequest.toString(), insightsRequest.getSocialChannels());
        int bucketSize = 0;
        List<String> channels = insightsRequest.getSocialChannels();
        if(CollectionUtils.isEmpty(channels)){
            channels = getAllSocialChannels();
        }
        int engagement = 0, impression = 0, clickCount = 0;
        int likeCount = 0 , commentCount = 0,shareCount = 0;
        double engRate = 0;
        Integer totalPosts = 0;//only for tiktok channel specific tab summary
        for (String channel : channels) {
            if(SocialChannel.YOUTUBE.getName().equals(channel)){
                continue; // YouTube channel is not supported for profile performance.
            }
            String ch = SocialChannel.getSocialChannelByName(channel).getName();
            SocialInsights execute = socialInsights
                    .getSocialInsightsChannel(ch)
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
//            if(channels.size() == 1 && channel.equalsIgnoreCase("tiktok")) {
            totalPosts += tiktokInsights.getTiktokPublishedPostInsights(insightsRequest);
//            }
            PerformanceSummaryResponse insightsFromEs =  execute.getPerformanceData(insightsRequest);
            if(Objects.isNull(insightsFromEs)) {
                continue;
            }
            engagement += insightsFromEs.getEngagements();
            impression += insightsFromEs.getImpressions();
            likeCount += insightsFromEs.getLikeCount();
            commentCount += insightsFromEs.getCommentCount();
            shareCount += insightsFromEs.getShareCount();
            clickCount += insightsFromEs.getPostLinkClicks();
            engRate += insightsFromEs.getEngRate();
        }
        response.setEngagements(engagement);
        response.setImpressions(impression);
        response.setPostLinkClicks(clickCount);
        response.setEngRate(reportsEsService.calculateEngagementRate(engagement, impression));
        response.setCommentCount(commentCount);
        response.setLikeCount(likeCount);
        response.setShareCount(shareCount);
        response.setPublishedPosts(totalPosts);
        return response;
    }

    private Double formatToTwoDecimalPlaces(Double number) {
        if(number == null) number = 0d;
        return Double.valueOf(new DecimalFormat("#.#").format(number));
    }

    @Override
    public List<ProfilePerformanceExcelResponse> getChannelPageReportForAll(InsightsRequest insightsRequest) throws Exception {

        log.info("getChannelPageInsightsForAll request: {}, channel {}", insightsRequest.toString(), insightsRequest.getSocialChannels());
        Map<String, PageReportEsData> map = new HashMap<>();
        Map<String, Map<String, Integer>> businessMap = new HashMap<>();
        int bucketSize = 0;
        List<String> channels = insightsRequest.getSocialChannels();
        if(CollectionUtils.isEmpty(channels)){
            channels = getAllSocialChannels();
        }

        for (String channel : channels) {
            String ch = SocialChannel.getSocialChannelByName(channel).getName();
            SocialInsights execute = socialInsights
                    .getSocialInsightsChannel(ch)
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));

            PageReportEsData insightV2EsData =  (PageReportEsData) execute.getPageReportESData(insightsRequest);
            if(Objects.isNull(insightV2EsData)) continue;
            Map<String, Integer> businessDetails = execute.getBusinessIdPageIdMapping(insightsRequest.getBusinessIds());

            businessMap.put(channel, businessDetails);

            log.info("getChannelPageInsightsForAll channel {}", channel);
            map.put(channel, insightV2EsData);
        }

        List<ProfilePerformanceExcelResponse> reportExcelData =  reportsEsService.getProfileReportData(map, insightsRequest);

        addBusinessDetailsToReport(reportExcelData, insightsRequest.getBusinessIds(), businessMap);

        return reportExcelData;

    }

    @Override
    public void backFillIgEngagement(IgBackFillInsightReq igBackFillInsightReq) {
        log.info("Req received to backFill data for page id: {}",igBackFillInsightReq.getPageId());
        instagramReportService.backFillIgEngagement(igBackFillInsightReq);
    }

    @Override
    public void backFillIgEngagementInit() {
        instagramReportService.backFillIgEngagementInit();
    }

    private void addBusinessDetailsToReport(List<ProfilePerformanceExcelResponse> reportExcelData, List<Integer> businessIds, Map<String, Map<String, Integer>> businessMap) {
        try {
            List<BusinessBizLiteDto> liteEntities =  iBusinessCoreService.getBusinessLiteDtoByBusinessIds(businessIds);


            reportExcelData.stream().forEach(v-> {

                String channel = v.getChannel();
                String profileId = v.getProfileId();
                Map<String, Integer> pageBusinessId = businessMap.get(channel);
                if(MapUtils.isNotEmpty(pageBusinessId)) {

                    Integer businessId =  pageBusinessId.get(profileId);

                    if(businessId != null) {
                        Optional<BusinessBizLiteDto> entity = liteEntities.stream().filter(i -> i.getId().equals(businessId)).findFirst();
                        if(entity.isPresent()) {
                            v.setLocationName(Objects.nonNull(entity.get().getName()) ? entity.get().getName() : entity.get().getAlias1());
                        }
                    }
                } else {
                    log.info("No businessId found for channel: {} and profileId: {}", channel, profileId);
                    v.setLocationName("");
                }
                v.setProfileId(null);

            });

        } catch (Exception e) {
            log.error("Error while adding business details to report", e);
        }
    }

    private void addBusinessDetailsToChannelSpecificReport(List<ProfilePerformanceExcelResponse> reportExcelData, List<Integer> businessIds, Map<String, Map<String, Integer>> businessMap) {
        try {
            List<BusinessBizLiteDto> liteEntities =  iBusinessCoreService.getBusinessLiteDtoByBusinessIds(businessIds);
            reportExcelData.stream().forEach(v-> {
                String channel = v.getChannel();
                String profileId = v.getProfileId();
                Map<String, Integer> pageBusinessId = businessMap.get(channel);
                if(MapUtils.isNotEmpty(pageBusinessId)) {
                    Integer businessId =  pageBusinessId.get(profileId);
                    if(businessId != null) {
                        Optional<BusinessBizLiteDto> entity = liteEntities.stream().filter(i -> i.getId().equals(businessId)).findFirst();
                        entity.ifPresent(businessBizLiteDto -> v.setLocationName(Objects.nonNull(businessBizLiteDto.getName()) ? businessBizLiteDto.getName() : businessBizLiteDto.getAlias1()));
                    }
                    v.setChannel(null);
                } else {
                    log.info("No businessId found for channel: {} and profileId: {}", channel, profileId);
                    v.setLocationName("");
                }
                v.setProfileId(null);

            });

        } catch (Exception e) {
            log.error("Error while adding business details to report", e);
        }
    }

    @Override
    public List<ProfilePerformanceExcelResponse> downloadExcelReportForSpecificTab(InsightsRequest insightsRequest, String channel) throws Exception {
        log.info("[downloadExcelReportForSpecificTab] insightsRequest: {}, channel {}", insightsRequest.toString(), channel);
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));

        List<ProfilePerformanceExcelResponse> reportExcelData = Collections.emptyList();

        switch (ch.toLowerCase()) {
            case TIKTOK:
                reportExcelData = handleTiktokSpecificTabReport(insightsRequest, ch, execute);
                break;
            case FACEBOOK:
                reportExcelData = handleFacebookSpecificTabReport(insightsRequest, ch, execute);
                break;
            default:
                break;
        }

        if (CollectionUtils.isNotEmpty(reportExcelData)) {
            Map<String, Map<String, Integer>> businessMap = new HashMap<>();
            Map<String, Integer> businessDetails = execute.getBusinessIdPageIdMapping(insightsRequest.getBusinessIds());
            businessMap.put(channel, businessDetails);
            addBusinessDetailsToChannelSpecificReport(reportExcelData, insightsRequest.getBusinessIds(), businessMap);
        }

        return reportExcelData;
    }

    private List<ProfilePerformanceExcelResponse> handleTiktokSpecificTabReport(InsightsRequest insightsRequest, String ch, SocialInsights execute) throws Exception {
        if (Objects.nonNull(insightsRequest.getReportType())
                && (Objects.equals(insightsRequest.getReportType(), GENDER_BASED_INSIGHTS.getName())
                || Objects.equals(insightsRequest.getReportType(), COUNTRY_BASED_INSIGHTS.getName())
                || Objects.equals(insightsRequest.getReportType(), CITY_BASED_INSIGHTS.getName()))) {
            return tiktokInsights.getTiktokDemographicReportData(insightsRequest, ch);
        } else if (Objects.equals(insightsRequest.getReportType(), REPORT_AUDIENCE_GROWTH.getName())) {
            return tiktokInsights.getTiktokAudienceGrowthReportData(insightsRequest, ch);
        } else {
            return handleStandardReport(insightsRequest, ch, execute);
        }
    }


    private List<ProfilePerformanceExcelResponse> handleFacebookSpecificTabReport(InsightsRequest insightsRequest, String ch, SocialInsights execute) throws Exception {
     if(Objects.nonNull(insightsRequest.getReportType())
             && (Objects.equals(insightsRequest.getReportType(), REPORT_PUBLISHED_POSTS_BEHAVIOUR.getName()))) {
            return facebookInsights.getPublishedPostBehaviourReportData(insightsRequest, SearchTemplate.REPORT_PUBLISHED_POSTS_BEHAVIOUR);
        } else {
            return facebookInsights.getFacebookInsightsReportingData(insightsRequest);
        }
    }


    private List<ProfilePerformanceExcelResponse> handleStandardReport(InsightsRequest insightsRequest, String ch, SocialInsights execute) throws Exception {
       log.info("[handleStandardReport] method called for report type: {}", insightsRequest.getReportType());
        PageReportEsData insightV2EsData = (PageReportEsData) execute.getPageReportESData(insightsRequest);
        if (Objects.isNull(insightV2EsData))
            return null;

        Map<String, PageReportEsData> map = new HashMap<>();
        map.put(ch, insightV2EsData);
        return reportsEsService.getProfileReportData(map, insightsRequest);
    }

    @Override
    public ExecutiveSummaryResponse getExecSummaryForAllChannel(ExecutiveSummaryRequest executiveSummaryRequest) {
        ExecutiveSummaryResponse response = new ExecutiveSummaryResponse();

        log.info("getExecSummaryForAllChannel request: {}, channel {}",
                executiveSummaryRequest.toString(), executiveSummaryRequest.getSocialChannels());
        List<String> channels = executiveSummaryRequest.getSocialChannels();
        if(CollectionUtils.isEmpty(channels)){
            channels = getAllSocialChannelsForSummaryExec();
        }
        Integer engagement = null, impression = null , clickCount = null , postCount = null  ,postCountAI = null
                , pageFollowers = null  , netFollowers = null  ;
        InsightsRequest request = createInsightRequest(executiveSummaryRequest);
        List<String> postInsights = Arrays.asList("postPublished","postPublishedAI","hoursSavedAI","socialROISummary");
        List<String> pageInsights = Arrays.asList("postImpression","pageFollowers","netFollowerGrowth",
                "engagement","engagementRate","linkClicks","socialROISummary");
        for (String channel : channels) {
            String ch = SocialChannel.getSocialChannelByName(channel).getName();
            SocialInsights execute = socialInsights.getSocialInsightsChannel(ch)
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
            PerformanceSummaryResponse summaryFromEs = null;
            ExecutivePostDataResponse executivePostDataResponse = null;
            if(pageInsights.stream().anyMatch(executiveSummaryRequest.getReportMetrix()::contains)) {
                summaryFromEs = execute.getPerformanceData(request);
            }
            if(postInsights.stream().anyMatch(executiveSummaryRequest.getReportMetrix()::contains)) {
                List<String> pageIds = execute.getPageIds(request.getBusinessIds());
                executivePostDataResponse=  reportsEsService.getPostData(request, pageIds);
            }

            if(Objects.nonNull(summaryFromEs) ) {
                if(Objects.nonNull(summaryFromEs.getEngagements()))
                    engagement = Objects.isNull(engagement) ? summaryFromEs.getEngagements() :  engagement + summaryFromEs.getEngagements();
                if(Objects.nonNull(summaryFromEs.getImpressions()))
                    impression = Objects.isNull(impression) ?  summaryFromEs.getImpressions() :  impression + summaryFromEs.getImpressions();
                if(Objects.nonNull(summaryFromEs.getFollowerGain()) && Objects.nonNull(summaryFromEs.getFollowerLost()))
                    netFollowers = Objects.isNull(netFollowers) ? (summaryFromEs.getFollowerGain() - summaryFromEs.getFollowerLost()) :  netFollowers + (summaryFromEs.getFollowerGain() - summaryFromEs.getFollowerLost());
                if(Objects.nonNull(summaryFromEs.getTotalFollowerCount()))
                    pageFollowers =  Objects.isNull(pageFollowers) ? summaryFromEs.getTotalFollowerCount() :  pageFollowers + summaryFromEs.getTotalFollowerCount();
                if(Objects.nonNull(summaryFromEs.getPostLinkClicks()))
                    clickCount = Objects.isNull(clickCount) ? summaryFromEs.getPostLinkClicks() :  clickCount + summaryFromEs.getPostLinkClicks();
            }
            if(Objects.nonNull(executivePostDataResponse) ) {
                postCountAI = Objects.isNull(postCountAI)  ? executivePostDataResponse.getAIPostCount() :  postCountAI + executivePostDataResponse.getAIPostCount();
                postCount = Objects.isNull(postCount) ? executivePostDataResponse.getPostCount() :  postCount + executivePostDataResponse.getPostCount();
            }
        }
        String report = executiveSummaryRequest.getReportMetrix().get(0);
        switch (report) {
            case "engagement":
                if (Objects.isNull(engagement)) return null;
                break;
            case "postImpression":
                if (Objects.isNull(impression)) return null;
                break;
            case "linkClicks":
                if (Objects.isNull(clickCount)) return null;
                break;
            case "engagementRate":
                if (Objects.isNull(engagement) || Objects.isNull(impression)) return null;
                break;
            case "netFollowerGrowth":
                if (Objects.isNull(netFollowers)) return null;
                break;
            case "postPublished":
                if (Objects.isNull(postCount)) return null;
                break;
            case "postPublishedAI":
            case "hoursSavedAI":
                if (Objects.isNull(postCountAI)) return null;
                break;
            case "pageFollowers":
                if (Objects.isNull(pageFollowers)) return null;
                break;
        }
        response.setEngagements(engagement);
        response.setPostImpression(impression);
        response.setLinkClicks(clickCount);
        response.setEngRate((Objects.isNull(engagement) || Objects.isNull(impression))
                ? null : reportsEsService.calculateEngagementRate(engagement,impression));
        response.setNetFollowers(netFollowers);
        response.setPostCount(postCount);
        response.setPostCountAI(postCountAI);
        response.setPageFollowers(pageFollowers);
        response.setHoursSavedAI(getHoursPostAIData(postCountAI));
        return response;
    }

    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest, String sortParam,
                                                               String sortOrder, Integer startIndex, Integer pageSize) {
        log.info("Request received for leadership report :{}",insightsRequest);

        // Process social channels from the request
        List<String> channels = new ArrayList<>();

        // Use socialChannels list if provided
        if(StringUtils.isNotEmpty(insightsRequest.getSocialChannel())){
            channels.add(insightsRequest.getSocialChannel());
        } else if (CollectionUtils.isNotEmpty(insightsRequest.getSocialChannels())) {
            channels.addAll(insightsRequest.getSocialChannels());
        }


        if (CollectionUtils.isEmpty(channels)) {
            log.info("Not a valid request - no social channels provided");
            return new LeadershipByPostsDataPoints();
        }

        // Set common parameters for all channel requests
        ReportSortingCriteria postSortingCriteria = ReportSortingCriteria.reportSortingCriteria(sortParam);
        SortOrder order = SortOrder.valueOf(sortOrder.toUpperCase());
        insightsRequest.setStart(DateTimeUtils.localToESFormat(insightsRequest.getStartDate()));
        insightsRequest.setEnd(DateTimeUtils.localToESFormat(insightsRequest.getEndDate()));

        // If only one channel, use the existing implementation
        if (channels.size() == 1) {
            String ch = SocialChannel.getSocialChannelByName(channels.get(0)).getName();
            SocialInsights execute = socialInsights
                    .getSocialInsightsChannel(ch)
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
            return execute.getPostLeadershipReport(insightsRequest, postSortingCriteria, order, startIndex, pageSize);
        }

        // For multiple channels, aggregate the results
        return getAggregatedPostLeadershipReport(insightsRequest, channels, postSortingCriteria, order, startIndex, pageSize);
    }

    /**
     * Aggregates leadership report data from multiple social channels
     */
    private LeadershipByPostsDataPoints getAggregatedPostLeadershipReport(LocationReportRequest insightsRequest,
                                                                        List<String> channels,
                                                                        ReportSortingCriteria postSortingCriteria,
                                                                        SortOrder order,
                                                                        Integer startIndex,
                                                                        Integer pageSize) {
        log.info("Aggregating leadership report for channels: {}", channels);

        // Map to store combined data points by location
        Map<Long, LeadershipByPostsResponse> aggregatedDataPoints = new HashMap<>();
        int totalRecords = 0;

        // Process each channel and aggregate the results
        for (String channel : channels) {
            try {
                String ch = SocialChannel.getSocialChannelByName(channel).getName();
                SocialInsights execute = socialInsights
                        .getSocialInsightsChannel(ch)
                        .orElseThrow(() -> new IllegalArgumentException("Invalid Request for channel: " + channel));

                // Get report for this channel
                // Use null for startIndex and a large value for pageSize to get all data points
                // We'll handle pagination after aggregation
                LeadershipByPostsDataPoints channelReport =
                        execute.getPostLeadershipReport(insightsRequest, postSortingCriteria, order, 0, Integer.MAX_VALUE);

                if (channelReport != null && CollectionUtils.isNotEmpty(channelReport.getDataPoints())) {
                    // Add this channel's total records to the overall count
                    if (channelReport.getTotalRecords() != null) {
                        totalRecords += channelReport.getTotalRecords();
                    }

                    // Merge data points by location
                    for (LeadershipByPostsResponse dataPoint : channelReport.getDataPoints()) {
                        long location = dataPoint.getLocation();

                        // If this location already exists in our aggregated map, update the values
                        if (aggregatedDataPoints.containsKey(location)) {
                            LeadershipByPostsResponse existing = aggregatedDataPoints.get(location);

                            // Aggregate metrics
                            existing.setPostCount(existing.getPostCount() + dataPoint.getPostCount());
                            existing.setImpressions(existing.getImpressions() + dataPoint.getImpressions());
                            existing.setTotalAudience(existing.getTotalAudience() + dataPoint.getTotalAudience());
                            existing.setNet(existing.getNet() + dataPoint.getNet());
                            existing.setEngagements(existing.getEngagements() + dataPoint.getEngagements());

                            // Recalculate engagement rate
                            if (existing.getImpressions() > 0) {
                                double engRate = (double) existing.getEngagements() / existing.getImpressions() * 100;
                                existing.setEngRate(engRate);
                            }
                        } else {
                            // Add new location to our aggregated map
                            aggregatedDataPoints.put(location, dataPoint);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error processing channel {} for leadership report: {}", channel, e.getMessage(), e);
            }
        }

        // Sort and paginate the aggregated results
        List<LeadershipByPostsResponse> sortedDataPoints = new ArrayList<>(aggregatedDataPoints.values());

        // Sort based on the requested criteria
        sortDataPoints(sortedDataPoints, postSortingCriteria, order);

        // Apply pagination
        List<LeadershipByPostsResponse> paginatedDataPoints = paginateDataPoints(sortedDataPoints, startIndex, pageSize);

        // Create the final response
        LeadershipByPostsDataPoints result = new LeadershipByPostsDataPoints();
        result.setDataPoints(paginatedDataPoints);
        result.setTotalRecords(totalRecords);

        return result;
    }

    /**
     * Sorts the data points based on the specified criteria and order
     */
    private void sortDataPoints(List<LeadershipByPostsResponse> dataPoints, ReportSortingCriteria criteria, SortOrder order) {
        if (CollectionUtils.isEmpty(dataPoints) || criteria == null) {
            return;
        }

        Comparator<LeadershipByPostsResponse> comparator = null;

        // Create comparator based on the sorting criteria
        if (criteria == ReportSortingCriteria.POST_ENGAGEMENT) {
            comparator = Comparator.comparing(LeadershipByPostsResponse::getEngagements);
        } else if (criteria == ReportSortingCriteria.POST_IMPRESSION) {
            comparator = Comparator.comparing(LeadershipByPostsResponse::getImpressions);
        } else if (criteria == ReportSortingCriteria.POST_PUBLISHED) {
            comparator = Comparator.comparing(LeadershipByPostsResponse::getPostCount);
        } else {
            // Default to sorting by engagements if criteria doesn't match
            log.info("Using default sorting by engagements for criteria: {}", criteria);
            comparator = Comparator.comparing(LeadershipByPostsResponse::getEngagements);
        }

        // Apply the sort order
        if (order == SortOrder.DESC) {
            comparator = comparator.reversed();
        }

        // Sort the list
        dataPoints.sort(comparator);
    }

    /**
     * Applies pagination to the data points list
     */
    private List<LeadershipByPostsResponse> paginateDataPoints(List<LeadershipByPostsResponse> dataPoints,
                                                             Integer startIndex, Integer pageSize) {
        if (CollectionUtils.isEmpty(dataPoints)) {
            return dataPoints;
        }

        // Default values if not provided
        int start = startIndex != null ? startIndex : 0;
        int size = pageSize != null ? pageSize : 10; // Default page size

        // Ensure start index is valid
        if (start < 0) {
            start = 0;
        }

        // If start is beyond the list size, return empty list
        if (start >= dataPoints.size()) {
            return new ArrayList<>();
        }

        // Calculate end index (exclusive)
        int end = Math.min(start + size, dataPoints.size());

        // Return the sublist
        return dataPoints.subList(start, end);
    }


    private InsightsRequest createInsightRequest(ExecutiveSummaryRequest executiveSummaryRequest) {

        InsightsRequest insightsRequest= new InsightsRequest();
        insightsRequest.setBusinessIds(executiveSummaryRequest.getBusinessIds());
        insightsRequest.setStartDate(executiveSummaryRequest.getStartDate());
        insightsRequest.setEndDate(executiveSummaryRequest.getEndDate());
        insightsRequest.setEnterpriseId(executiveSummaryRequest.getEnterpriseId());
        insightsRequest.setCustomStartDate(executiveSummaryRequest.getCustomStartDate());
        insightsRequest.setDays(executiveSummaryRequest.getDays());
        insightsRequest.setGroupByType(executiveSummaryRequest.getGroupByType());
        insightsRequest.setIncludeExtendedBounds(executiveSummaryRequest.getIncludeExtendedBounds());
        insightsRequest.setMonths(executiveSummaryRequest.getMonths());
        insightsRequest.setSocialChannels(executiveSummaryRequest.getSocialChannels());
        return insightsRequest;
    }

    private Integer getHoursPostAIData(Integer postCountAI) {
        Integer hoursSavedAI = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getHoursSavedPostAI();
        if (Objects.isNull(hoursSavedAI)|| hoursSavedAI == 0 || Objects.isNull(postCountAI)) {
            return 0; // Return 0 if the value is invalid or no time is saved
        }
        // Return the calculated saved hours
        return (postCountAI * hoursSavedAI) / 60;

    }

    @Override
    public Object getTopPerformingPosts(InsightsRequest insightsRequest, String channel, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        log.info("insightsRequest for top performing post : {}, for channel {}", insightsRequest.toString(), channel);
        PostSortingCriteria postSortingCriteria = PostSortingCriteria.postSortingCriteria(sortParam);
        PostSortingOrder postSortingOrder = PostSortingOrder.postSortingOrder(sortOrder);
        if(postSortingCriteria == null || postSortingOrder == null) {
            throw new BadRequestException("Invalid sorting attributes");
        }
        String ch=null;
        try {
            ch = SocialChannel.getSocialChannelByName(channel).getName();
        } catch (Exception e) {
            if (channel.equals("all")) {
                ch = "allChannels";
            }
        }

        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
        return execute.getTopPostsInsightsFromES(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }
}