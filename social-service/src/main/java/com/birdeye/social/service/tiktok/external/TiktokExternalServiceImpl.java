package com.birdeye.social.service.tiktok.external;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialDomainAppCredsRepository;
import com.birdeye.social.entities.BusinessTiktokAccounts;
import com.birdeye.social.entities.SocialDomainAppCreds;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.tiktok.*;
import com.birdeye.social.model.tiktok.arbor.TiktokAccountDetailsResponse;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

@Service
public class TiktokExternalServiceImpl implements TiktokExternalService {

    @Autowired
    private SocialDomainAppCredsRepository socialAppCredRepository;


    @Autowired
    @Qualifier("socialRestTemplate")
    private RestTemplate socialRestTemplate;

    private static final Logger LOG	= LoggerFactory.getLogger(TiktokExternalServiceImpl.class);

    public final String TIKTOK_AUTHENTICATION_URL = "https://www.tiktok.com/v2/auth/authorize/";

    public final String TIKTOK_TOKEN_URL = "https://business-api.tiktok.com/open_api/v1.3/tt_user/oauth2/token/";

    public final String TIKTOK_REFRESH_TOKEN_URL = "https://business-api.tiktok.com/open_api/v1.3/tt_user/oauth2/refresh_token/";

    public final String TIKTOK_ACCOUNT_INFO = "https://business-api.tiktok.com/open_api/v1.3/business/get/";

    public final String TIKTOK_VIDEO_POSTING = "https://business-api.tiktok.com/open_api/v1.3/business/video/publish/";

    public final String TIKTOK_RECOMMENDED_HASHTAGS = "https://business-api.tiktok.com/open_api/v1.3/business/hashtag/suggestion/?business_id=%s&keyword=%s&language=%s";

    public final String TIKTOK_AUTHENTICATION_SCOPE = "user.info.basic,user.info.username,user.info.stats,user.info.profile,user.account.type,user.insights,video.list,video.insights,comment.list,comment.list.manage,video.publish,video.upload,biz.spark.auth";

    public final String TIKTOK_ACCOUNT_INFO_FIELDS ="[\"username\",\"display_name\", \"profile_image\", \"is_business_account\", \"profile_deep_link\",  \"is_verified\", \"bio_description\"]";

    public static final String TIKTOK_REDIRECT_URI = "tiktok.redirect.uri";
    public static final String TIKTOK_WHITELABEL_REDIRECT_URI = "tiktok.whitelabel.redirect.uri";
    public static final String TIKTOK_OPEN_URL_REDIRECT_URI = "tiktok.openUrl.redirect.uri";

    @Override
    public String getAuthLoginUrl(String origin, String redirectUri) {
        try {
            return getAuthenticationUrl(origin, redirectUri);
        } catch (Exception ex) {
            LOG.info("Something went wrong while generating auth url for tiktok with error ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
    }

    @Override
    public TiktokAccessTokenDataResponse generateRefreshToken(String code, String redirectUri) {
        ResponseEntity<TiktokAccessTokenBaseResponse> response = null;

        try {
            LinkedHashMap<String , String>  parametersMap =  getRefreshTokenParam(code, redirectUri);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<LinkedHashMap<String, String>> requestEntity = new HttpEntity<>(parametersMap, headers);

            LOG.info("Received request for API generate Tiktok refresh token URL {} and parameters {}", TIKTOK_TOKEN_URL, parametersMap);
            response = socialRestTemplate.exchange(TIKTOK_TOKEN_URL,
                    HttpMethod.POST,
                    requestEntity,
                    TiktokAccessTokenBaseResponse.class);

        } catch (HttpStatusCodeException e) {
            // handle and throw new Error
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());

        } catch (Exception ex) {
            LOG.info("Something went wrong while generating auth token for tiktok with error ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
        return response.getBody().getData();
    }

    @Override
    public TiktokAccessTokenDataResponse generateRefreshToken(String refreshToken) {
        ResponseEntity<TiktokAccessTokenBaseResponse> response = null;

        try {
            LinkedHashMap<String , String>  parametersMap =  getAccessTokenParam(refreshToken);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<LinkedHashMap<String, String>> requestEntity = new HttpEntity<>(parametersMap, headers);

            LOG.info("Received request for API generate Tiktok refresh token URL {} and parameters {}", TIKTOK_TOKEN_URL, parametersMap);
            response = socialRestTemplate.exchange(TIKTOK_REFRESH_TOKEN_URL, HttpMethod.POST, requestEntity, TiktokAccessTokenBaseResponse.class);
            // TODO : add log
        } catch (HttpStatusCodeException e) {
            // handle and throw new Error
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());

        } catch (Exception ex) {
            LOG.info("Something went wrong while generating auth token for tiktok with error ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
        return response.getBody().getData();
    }

    /**
     * Evict tiktok access token from Redis
     * @param refreshToken
     */
    @Override
    @CacheEvict(value = "tiktokAccessToken", key = "#refreshToken")
    public void evictTiktokAccessToken(String refreshToken) {
        LOG.info("Evicting cache for tiktok refresh token: {}", refreshToken);
    }

    @Override
    @Cacheable(value = "tiktokAccessToken", key = "#refreshToken", unless = "#result == null")
    public TiktokAccessTokenDataResponse generateAccessToken(String refreshToken) {
        ResponseEntity<TiktokAccessTokenBaseResponse> response = null;

        try {
            LinkedHashMap<String , String>  parametersMap =  getAccessTokenParam(refreshToken);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            HttpEntity<LinkedHashMap<String, String>> requestEntity = new HttpEntity<>(parametersMap, headers);

            LOG.info("Received request for API generate Tiktok access token URL {} and parameters {}", TIKTOK_REFRESH_TOKEN_URL, parametersMap);
            response = socialRestTemplate.exchange(TIKTOK_REFRESH_TOKEN_URL, HttpMethod.POST, requestEntity, TiktokAccessTokenBaseResponse.class);

        } catch (HttpStatusCodeException e) {
            // handle and throw new Error
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());

        } catch (Exception ex) {
            LOG.info("Something went wrong while generating auth access token for tiktok with error ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
        return response.getBody().getData();
    }

    @Override
    public TiktokAccountDetailsResponse getAccountDetails(String userId, String accessToken) {
        ResponseEntity<TiktokAccountDetailsResponse> response = null;

        try {
            MultiValueMap<String , String>  parametersMap =  getAccountDetailsParam(userId);

            HttpHeaders headers = new HttpHeaders();
            headers.set("Access-Token", accessToken);
            headers.set(HttpHeaders.CONTENT_TYPE, "application/json");

            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(headers);

            LOG.info("Received request for API generate Tiktok refresh token URL {} and parameters {}", TIKTOK_TOKEN_URL, parametersMap);
            response = socialRestTemplate.exchange(buildUri(TIKTOK_ACCOUNT_INFO, parametersMap), HttpMethod.GET, requestEntity, TiktokAccountDetailsResponse.class);

        } catch (HttpStatusCodeException e) {
            // handle and throw new Error
        } catch (Exception ex) {
            LOG.info("Something went wrong while generating auth token for tiktok with error ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
        return response.getBody();
    }

    private URI buildUri(String path, MultiValueMap<String, String> parameters) {
        if(Objects.isNull(parameters)) {
            return org.springframework.social.support.URIBuilder.fromUri(path).build();
        }
        return org.springframework.social.support.URIBuilder.fromUri(path).queryParams(parameters).build();
    }

    private MultiValueMap<String, String> getAccountDetailsParam(String profileId) {

        MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
        parametersMap.add("fields", TIKTOK_ACCOUNT_INFO_FIELDS);
        parametersMap.add("business_id", profileId);

        return parametersMap;
    }

    private LinkedHashMap<String, String> getRefreshTokenParam(String code, String redirectUri) {
        SocialDomainAppCreds domainSocialAppCred = generateClientConfig();

        LinkedHashMap<String, String> parametersMap = new LinkedHashMap<>();

        parametersMap.put("client_id", domainSocialAppCred.getAppKey());
        parametersMap.put("client_secret", domainSocialAppCred.getAppSecret());
        parametersMap.put("auth_code", code);
        parametersMap.put("grant_type", "authorization_code");
        parametersMap.put("redirect_uri", redirectUri);

        return parametersMap;
    }

    private LinkedHashMap<String, String> getAccessTokenParam(String refreshToken) {
        SocialDomainAppCreds domainSocialAppCred = generateClientConfig();

        LinkedHashMap<String, String> parametersMap = new LinkedHashMap<>();

        parametersMap.put("client_id", domainSocialAppCred.getAppKey());
        parametersMap.put("client_secret", domainSocialAppCred.getAppSecret());
        parametersMap.put("grant_type", "refresh_token");
        parametersMap.put("refresh_token", refreshToken);

        return parametersMap;
    }


    private String getAuthenticationUrl(String origin, String redirectUri) throws URISyntaxException {

        if(Objects.isNull(redirectUri)) {// case for Enterprise, Reseller and Whitelabel
            redirectUri = getTiktokRedirectUri(origin);
        }
        else { // case for open-url
            redirectUri = getTiktokRedirectUriForOpenUrl();
        }

        SocialDomainAppCreds domainSocialAppCred = generateClientConfig();

        String csrfState = UUID.randomUUID().toString().replace("-", "").substring(0, 16);

        URIBuilder authURL = getUriBuilder(redirectUri, domainSocialAppCred, csrfState);
        return authURL.toString();
    }

    /**
     * Origin for non whitelabel user --> app.birdeye.com :: method returns app.birdeye.com
     * Origin for whitelabel user --> {whiteLabel}.com :: method returns social-integration.com
     * @param origin
     * @return
     */
    private String getTiktokRedirectUri(String origin) {
        String redirectUrl =  CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(TIKTOK_REDIRECT_URI, "ppapp.birdeye.com");
        return StringUtils.contains(origin, redirectUrl) ? redirectUrl :
                CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(TIKTOK_WHITELABEL_REDIRECT_URI, redirectUrl);
    }

    private String getTiktokRedirectUriForOpenUrl() {
        return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(TIKTOK_OPEN_URL_REDIRECT_URI);
    }

    @Cacheable(value = "tiktokArbor", key = "-secret", unless = "#result == null")
    private SocialDomainAppCreds generateClientConfig() {
        SocialDomainAppCreds domainSocialAppCreds = socialAppCredRepository.findByAppType(SocialChannel.TIKTOK.getName().toLowerCase());

        return domainSocialAppCreds;
    }

    @NotNull
    private URIBuilder getUriBuilder(String redirectUri, SocialDomainAppCreds domainSocialAppCred, String csrfState) throws URISyntaxException {
        URIBuilder authURL = new URIBuilder(TIKTOK_AUTHENTICATION_URL);
        authURL.addParameter("redirect_uri", redirectUri);
//        authURL.addParameter("prompt", "login"); // not required in business app
        authURL.addParameter("client_key", domainSocialAppCred.getAppKey());
        authURL.addParameter("scope", TIKTOK_AUTHENTICATION_SCOPE);
        authURL.addParameter("response_type", "code");
        authURL.addParameter("disable_auto_auth", "1"); // to enable auth for multiple account add

//        authURL.addParameter("state", csrfState); // not required in business app
        return authURL;
    }

    @Override
    public TiktokVideoPostingResponse postVideo(TiktokVideoPostingRequest videoPostingRequest, String accessToken) {
        ResponseEntity<TiktokVideoPostingResponse> response = null;

        try {

            HttpHeaders headers = new HttpHeaders();
            headers.set("Access-Token", accessToken);
            headers.set(HttpHeaders.CONTENT_TYPE, "application/json");

            HttpEntity<TiktokVideoPostingRequest> requestEntity = new HttpEntity<>(videoPostingRequest, headers);

            LOG.info("[Tiktok] Received request for API to post {} and request {}", TIKTOK_VIDEO_POSTING, videoPostingRequest);
            response = socialRestTemplate.exchange(buildUri(TIKTOK_VIDEO_POSTING, null), HttpMethod.POST, requestEntity, TiktokVideoPostingResponse.class);
            LOG.info("[Tiktok] Response for API to post {} and response: {}", TIKTOK_VIDEO_POSTING, response.getBody());
        } catch (HttpStatusCodeException e) {
            LOG.info("Something went wrong while generating auth token for tiktok");
            return handleError(e);
        } catch (Exception ex) {
            LOG.info("Something went wrong while generating auth token for tiktok with error ", ex);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
        return response.getBody();
    }

    private TiktokVideoPostingResponse handleError(HttpStatusCodeException e) {
        LOG.info("error in tiktok api: {}", e.getResponseBodyAsString());
        TiktokVideoPostingResponse tiktokVideoPostingResponse;
        if(Objects.isNull(e)) throw new BirdeyeSocialException("Unknown error occurred");

        try {
            tiktokVideoPostingResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), TiktokVideoPostingResponse.class);
        } catch (Exception ex) {
            LOG.info("error occurred while parsing https status exception: {}", ex.getMessage());
            throw new BirdeyeSocialException("Unknown error occurred");
        }
        return tiktokVideoPostingResponse;
    }

    @Override
    public List<TikTokHashtagResponse.HashtagResponse> fetchRecommendedHashtags(String keyword, BusinessTiktokAccounts tiktokAccount) {
        String url = String.format(TIKTOK_RECOMMENDED_HASHTAGS, tiktokAccount.getProfileId(),
                com.birdeye.social.utils.StringUtils.encodeToUtf8(keyword), "en"
        );

        HttpHeaders headers = new HttpHeaders();
        TiktokAccessTokenDataResponse tiktokAccessTokenDataResponse = generateRefreshToken(tiktokAccount.getRefreshToken());
        if (Objects.isNull(tiktokAccessTokenDataResponse)) {
            LOG.error("Failed to generate access token for TikTok account: {}", tiktokAccount.getProfileId());
            return Collections.emptyList();
        }
        headers.set("Access-Token", tiktokAccessTokenDataResponse.getAccessToken());

        HttpEntity<String> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<TikTokHashtagResponse> response = socialRestTemplate.exchange(
                    url, HttpMethod.GET, entity, TikTokHashtagResponse.class
            );

            if (response.getBody() != null && response.getBody().getData() != null) {
                return response.getBody().getData().getSuggestions();
            }
        } catch (RestClientException e) {
            LOG.error("RestClientException Error fetching hashtags from TikTok API: {}", e.getMessage(), e);
        } catch (Exception e) {
            LOG.error("Error fetching hashtags from TikTok API: {}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }
}
