-- Database optimization indexes for BTP (Best Time to Post) queries
-- These indexes will significantly improve the performance of the optimized queries

-- Primary composite index for the main query filters
-- This index covers all the WHERE clause conditions in the optimized query
CREATE INDEX idx_btp_day_wise_main_query 
ON btp_day_wise_data (country, channel, category, report_type, day, value);

-- Alternative index with different column order for better selectivity
-- Use this if the above index doesn't provide optimal performance
-- CREATE INDEX idx_btp_day_wise_alt_query 
-- ON btp_day_wise_data (report_type, country, channel, category, day, value);

-- Index specifically for date range queries
-- This helps with the day > :day AND day < :toTime conditions
CREATE INDEX idx_btp_day_wise_date_range 
ON btp_day_wise_data (day, value);

-- Covering index that includes all columns needed by the query
-- This can eliminate the need to access the actual table data
CREATE INDEX idx_btp_day_wise_covering 
ON btp_day_wise_data (country, channel, category, report_type, day, value, id);

-- Index for the week-based queries
CREATE INDEX idx_btp_day_wise_week_query 
ON btp_day_wise_data (report_type, channel, country, category, day, value);

-- Performance analysis queries to check index usage
-- Run these after creating indexes to verify they are being used

-- Check index usage for the main query
-- EXPLAIN SELECT b.* FROM btp_day_wise_data b 
-- WHERE b.country = 'US' AND b.channel = 'facebook' AND b.category = 'restaurant' 
-- AND b.report_type = 'INCREASE_ENGAGEMENT' AND b.day > '2024-01-01' AND b.day < '2024-12-31' 
-- ORDER BY b.day, b.value DESC;

-- Check current indexes on the table
-- SHOW INDEX FROM btp_day_wise_data;

-- Monitor query performance
-- SELECT * FROM information_schema.PROCESSLIST WHERE db = 'your_database_name';

-- Notes:
-- 1. The main composite index should be created first as it provides the most benefit
-- 2. Monitor query performance after creating indexes
-- 3. Consider dropping unused indexes to save storage space
-- 4. Update table statistics after creating indexes: ANALYZE TABLE btp_day_wise_data;
-- 5. These indexes will improve SELECT performance but may slightly slow down INSERT/UPDATE operations
