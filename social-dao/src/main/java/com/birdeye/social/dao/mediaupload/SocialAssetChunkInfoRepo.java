package com.birdeye.social.dao.mediaupload;

import com.birdeye.social.entities.mediaupload.SocialAssetChunkInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SocialAssetChunkInfoRepo extends JpaRepository<SocialAssetChunkInfo,Integer> {
    boolean existsByAssetId(Integer assetId);

    @Query("select count(s) from SocialAssetChunkInfo s where s.assetId = (:assetId) and s.sourceId = (:sourceId)")
    Integer findCountByAssetIdAndSourceId(@Param("assetId") Integer assetId, @Param("sourceId") Integer sourceId);

    SocialAssetChunkInfo findByAssetIdAndSequenceIdAndSourceId(Integer assetId, Integer sequenceId, Integer sourceId);

    List<SocialAssetChunkInfo> findByAssetId(Integer assetId);
    List<SocialAssetChunkInfo> findByAssetIdAndSourceId(Integer assetId, Integer sourceId);

    @Query("select s.sequenceId from SocialAssetChunkInfo s where s.assetId = (:assetId) and s.sourceId = (:sourceId)")
    List<Integer> findSequenceIdsByAssetIdAndSourceId(@Param("assetId") Integer assetId,@Param("sourceId") Integer sourceId);
}
