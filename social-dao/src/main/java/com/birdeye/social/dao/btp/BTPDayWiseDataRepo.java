package com.birdeye.social.dao.btp;

import com.birdeye.social.entities.btp.BTPDayWiseData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface BTPDayWiseDataRepo extends JpaRepository<BTPDayWiseData,Integer> {

    @Query(value = "SELECT b1.* FROM btp_day_wise_data b1 WHERE ( SELECT COUNT(*) FROM btp_day_wise_data b2 " +
            "WHERE b2.country = b1.country AND b2.channel = b1.channel AND b2.category = b1.category " +
            "AND b2.report_type = b1.report_type AND DATE(b2.day) = DATE(b1.day) AND b2.value > b1.value " +
            "AND b2.day > :day AND b2.day < :toTime) < :limit AND b1.country = :country AND b1.channel = :channel " +
            "AND b1.category = :category AND b1.report_type = :reportType AND b1.day > :day and b1.day < :toTime ORDER BY b1.value DESC",nativeQuery = true)
    List<BTPDayWiseData> findByCountryAndChannelAndCategoryAndReportTypeAndDayGreaterThan(@Param("country") String country,
                                                                                          @Param("channel") String channel,
                                                                                          @Param("category") String category,
                                                                                          @Param("reportType") String reportType,
                                                                                          @Param("day") Date fromTime,
                                                                                          @Param("toTime") Date toTime,
                                                                                          @Param("limit") Integer limit);

    /**
     * Optimized query without correlated subquery for better performance
     * Uses simple filtering and ordering, letting application logic handle top 3 per day
     * This approach is much faster than correlated subqueries and avoids DATE() function calls
     */
    @Query(value = "SELECT b.* FROM btp_day_wise_data b " +
            "WHERE b.country = :country AND b.channel = :channel AND b.category = :category " +
            "AND b.report_type = :reportType AND b.day > :day AND b.day < :toTime " +
            "ORDER BY b.day, b.value DESC", nativeQuery = true)
    List<BTPDayWiseData> findTop3TimesPerDay(@Param("country") String country,
                                             @Param("channel") String channel,
                                             @Param("category") String category,
                                             @Param("reportType") String reportType,
                                             @Param("day") Date fromTime,
                                             @Param("toTime") Date toTime);



    @Query(value = "select b.* from btp_day_wise_data b where b.report_type = (:reportType) " +
            "and b.channel = :channel and b.country = :country and b.category = (:category) and  b.day BETWEEN (:day) " +
            "AND (:endTime) order by b.value desc",nativeQuery = true)
    List<BTPDayWiseData> findTop3TimesPerWeek(@Param("country") String country,
                                              @Param("channel") String channel,
                                              @Param("category") String category,
                                              @Param("reportType") String reportType,
                                              @Param("day") Date fromTime,
                                              @Param("endTime") Date endTime);
}
